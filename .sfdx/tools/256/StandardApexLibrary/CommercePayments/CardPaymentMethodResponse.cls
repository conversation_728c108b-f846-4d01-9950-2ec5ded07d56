global class CardPaymentMethodResponse {
	global CardPaymentMethodResponse() { }
	global Object clone() { }
	global void setAccountId(Id accountId) { }
	global void setAutoPay(Boolean autoPay) { }
	global void setCardBin(String cardBin) { }
	global void setCardCategory(commercepayments.CardCategory cardCategory) { }
	global void setCardHolderFirstName(String cardHolderFirstName) { }
	global void setCardHolderLastName(String cardHolderLastName) { }
	global void setCardHolderName(String cardHolderName) { }
	global void setCardLastFour(String cardLastFour) { }
	global void setCardType(String cardType) { }
	global void setCardTypeCategory(commercepayments.CardType cardTypeCategory) { }
	global void setComments(String comments) { }
	global void setDisplayCardNumber(String displayCardNumber) { }
	global void setEmail(String email) { }
	global void setExpiryMonth(Integer expiryMonth) { }
	global void setExpiryYear(Integer expiryYear) { }
	global void setNickName(String nickName) { }
	global void setStartMonth(Integer startMonth) { }
	global void setStartYear(Integer startYear) { }

}