global class PlaceOrderExecutor {
	global PlaceOrderExecutor() { }
	global Object clone() { }
	global static commerceorders.PlaceOrderResult execute(commerceorders.GraphRequest graphRequest, commerceorders.PricingPreferenceEnum pricingPreferenceEnum, commerceorders.CatalogRatesPreferenceEnum catalogRatesPreferenceEnum, commerceorders.ConfigurationInputEnum configurationInputEnum, commerceorders.ConfigurationOptionsInput configurationOptionsInput) { }
	global static commerceorders.PlaceOrderResult execute(commerceorders.GraphRequest graphRequest, commerceorders.PricingPreferenceEnum pricingPreferenceEnum, commerceorders.ConfigurationInputEnum configurationInputEnum, commerceorders.ConfigurationOptionsInput configurationOptionsInput) { }
	global static commerceorders.PlaceOrderResult execute(commerceorders.GraphRequest graphRequest) { }

}