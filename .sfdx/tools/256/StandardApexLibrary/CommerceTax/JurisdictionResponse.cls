global class JurisdictionResponse {
	global JurisdictionResponse() { }
	global Object clone() { }
	global java:commerce.tax.impl.engine.integration.response.JurisdictionEngineResponse getDelegate() { }
	global void setCountry(String country) { }
	global void setId(String id) { }
	global void setLevel(String level) { }
	global void setName(String name) { }
	global void setRegion(String region) { }
	global void setStateAssignedNumber(String stateAssignedNo) { }

}