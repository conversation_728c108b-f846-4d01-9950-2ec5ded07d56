global class WriteOffPostedInvoiceResult {
	global ConnectApi.WriteOffPostedInvoiceOutputErrorResponse errors;
	global String invoiceId;
	global String requestIdentifier;
	global Boolean success;
	global WriteOffPostedInvoiceResult() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}