global class WebStoreMetaConfig {
	global String adAccountExtKey;
	global String businessManagerExtKey;
	global String catalogExtKey;
	global String commerceMerchantSettingsExtKey;
	global String id;
	global String pageExtKey;
	global String profileExtKey;
	global String trackerExtKey;
	global String webStoreId;
	global WebStoreMetaConfig() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}