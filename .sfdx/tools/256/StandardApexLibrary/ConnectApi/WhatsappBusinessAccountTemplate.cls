global class WhatsappBusinessAccountTemplate {
	global String id;
	global String json;
	global String language;
	global String name;
	global ConnectApi.WhatsappBusinessAccountRecordDetail recordDetail;
	global String status;
	global String wabaId;
	global WhatsappBusinessAccountTemplate() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}