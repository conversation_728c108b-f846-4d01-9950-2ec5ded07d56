global class WishlistItemCollection {
	global String currencyIsoCode;
	global String currentPageToken;
	global String currentPageUrl;
	global Boolean hasErrors;
	global List<ConnectApi.WishlistItem> items;
	global String nextPageToken;
	global String nextPageUrl;
	global String previousPageToken;
	global String previousPageUrl;
	global WishlistItemCollection() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}