# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_30_104234) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "account_users", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "user_id", null: false
    t.string "role", default: "member", null: false
    t.datetime "joined_at", null: false
    t.jsonb "permissions", default: {}
    t.boolean "active", default: true
    t.datetime "last_active_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "user_id"], name: "index_account_users_on_account_id_and_user_id", unique: true
    t.index ["account_id"], name: "index_account_users_on_account_id"
    t.index ["active"], name: "index_account_users_on_active"
    t.index ["joined_at"], name: "index_account_users_on_joined_at"
    t.index ["role"], name: "index_account_users_on_role"
    t.index ["user_id"], name: "index_account_users_on_user_id"
  end

  create_table "accounts", force: :cascade do |t|
    t.string "name", null: false
    t.string "subdomain", null: false
    t.bigint "owner_id", null: false
    t.string "status", default: "active", null: false
    t.jsonb "settings", default: {}
    t.string "time_zone", default: "UTC"
    t.string "billing_email"
    t.string "plan", default: "free"
    t.integer "projects_limit", default: 1
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_accounts_on_active"
    t.index ["owner_id"], name: "index_accounts_on_owner_id"
    t.index ["plan"], name: "index_accounts_on_plan"
    t.index ["status"], name: "index_accounts_on_status"
    t.index ["subdomain"], name: "index_accounts_on_subdomain", unique: true
  end

  create_table "landing_pages", force: :cascade do |t|
    t.bigint "project_id", null: false
    t.string "title", null: false
    t.string "subtitle"
    t.text "description"
    t.string "hero_image_url"
    t.string "primary_color", default: "#3B82F6"
    t.string "secondary_color", default: "#6366F1"
    t.text "custom_css"
    t.string "custom_domain"
    t.boolean "published", default: false, null: false
    t.string "slug", null: false
    t.string "meta_title", limit: 60
    t.text "meta_description"
    t.string "meta_keywords"
    t.string "robots_meta", default: "index, follow"
    t.string "canonical_url"
    t.string "og_title", limit: 60
    t.text "og_description"
    t.string "og_image_url"
    t.string "og_type", default: "website"
    t.string "twitter_title", limit: 60
    t.text "twitter_description"
    t.string "twitter_image_url"
    t.string "twitter_card", default: "summary_large_image"
    t.text "schema_markup"
    t.integer "page_views", default: 0
    t.integer "unique_visitors", default: 0
    t.datetime "last_crawled_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "account_id", null: false
    t.index ["account_id"], name: "index_landing_pages_on_account_id"
    t.index ["created_at"], name: "index_landing_pages_on_created_at"
    t.index ["custom_domain"], name: "index_landing_pages_on_custom_domain", unique: true
    t.index ["page_views"], name: "index_landing_pages_on_page_views"
    t.index ["project_id", "published"], name: "index_landing_pages_on_project_id_and_published"
    t.index ["project_id"], name: "index_landing_pages_on_project_id"
    t.index ["published"], name: "index_landing_pages_on_published"
    t.index ["slug"], name: "index_landing_pages_on_slug", unique: true
  end

  create_table "projects", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.string "name", null: false
    t.string "slug", null: false
    t.text "description"
    t.string "status", default: "draft", null: false
    t.jsonb "settings", default: {}
    t.jsonb "custom_fields", default: []
    t.jsonb "branding", default: {}
    t.jsonb "analytics", default: {}
    t.string "website_url"
    t.string "logo_url"
    t.string "custom_domain"
    t.boolean "public_stats", default: true
    t.boolean "email_collection_enabled", default: true
    t.boolean "double_opt_in", default: true
    t.datetime "launched_at"
    t.datetime "archived_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "name"], name: "index_projects_on_account_id_and_name"
    t.index ["account_id"], name: "index_projects_on_account_id"
    t.index ["custom_domain"], name: "index_projects_on_custom_domain"
    t.index ["launched_at"], name: "index_projects_on_launched_at"
    t.index ["public_stats"], name: "index_projects_on_public_stats"
    t.index ["slug"], name: "index_projects_on_slug", unique: true
    t.index ["status"], name: "index_projects_on_status"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at"
    t.string "first_name"
    t.string "last_name"
    t.string "role", default: "user", null: false
    t.string "time_zone", default: "UTC"
    t.boolean "admin", default: false
    t.string "phone_number"
    t.jsonb "preferences", default: {}
    t.jsonb "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin"], name: "index_users_on_admin"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["role"], name: "index_users_on_role"
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true
  end

  create_table "waitlist_entries", force: :cascade do |t|
    t.bigint "project_id", null: false
    t.bigint "account_id", null: false
    t.string "email", null: false
    t.string "name"
    t.string "phone_number"
    t.string "status", default: "pending", null: false
    t.integer "position"
    t.string "referral_code"
    t.string "referred_by"
    t.jsonb "custom_data", default: {}
    t.datetime "confirmed_at"
    t.string "confirmation_token"
    t.datetime "confirmation_sent_at"
    t.string "ip_address"
    t.string "user_agent"
    t.string "source"
    t.integer "referrals_count", default: 0
    t.boolean "email_verified", default: false
    t.datetime "notified_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "landing_page_id"
    t.string "first_name"
    t.string "last_name"
    t.string "company"
    t.string "phone"
    t.string "referrer"
    t.datetime "unsubscribed_at"
    t.index ["account_id"], name: "index_waitlist_entries_on_account_id"
    t.index ["confirmation_token"], name: "index_waitlist_entries_on_confirmation_token", unique: true
    t.index ["confirmed_at"], name: "index_waitlist_entries_on_confirmed_at"
    t.index ["email"], name: "index_waitlist_entries_on_email"
    t.index ["landing_page_id"], name: "index_waitlist_entries_on_landing_page_id"
    t.index ["position"], name: "index_waitlist_entries_on_position"
    t.index ["project_id", "email"], name: "index_waitlist_entries_on_project_id_and_email", unique: true
    t.index ["project_id"], name: "index_waitlist_entries_on_project_id"
    t.index ["referral_code"], name: "index_waitlist_entries_on_referral_code", unique: true
    t.index ["referred_by"], name: "index_waitlist_entries_on_referred_by"
    t.index ["source"], name: "index_waitlist_entries_on_source"
    t.index ["status"], name: "index_waitlist_entries_on_status"
  end

  add_foreign_key "account_users", "accounts"
  add_foreign_key "account_users", "users"
  add_foreign_key "accounts", "users", column: "owner_id"
  add_foreign_key "landing_pages", "accounts"
  add_foreign_key "landing_pages", "projects"
  add_foreign_key "projects", "accounts"
  add_foreign_key "waitlist_entries", "accounts"
  add_foreign_key "waitlist_entries", "landing_pages"
  add_foreign_key "waitlist_entries", "projects"
end
