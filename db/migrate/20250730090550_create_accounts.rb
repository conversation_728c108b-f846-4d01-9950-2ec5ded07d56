class CreateAccounts < ActiveRecord::Migration[8.0]
  def change
    create_table :accounts do |t|
      t.string :name, null: false
      t.string :subdomain, null: false
      t.references :owner, null: false, foreign_key: { to_table: :users }
      t.string :status, default: 'active', null: false
      t.jsonb :settings, default: {}
      t.string :time_zone, default: 'UTC'
      t.string :billing_email
      t.string :plan, default: 'free'
      t.integer :projects_limit, default: 1
      t.boolean :active, default: true
      
      t.timestamps
    end
    
    add_index :accounts, :subdomain, unique: true
    add_index :accounts, :status
    add_index :accounts, :plan
    add_index :accounts, :active
  end
end
