class CreateAccountUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :account_users do |t|
      t.references :account, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :role, null: false, default: 'member'
      t.datetime :joined_at, null: false
      t.jsonb :permissions, default: {}
      t.boolean :active, default: true
      t.datetime :last_active_at

      t.timestamps
    end
    
    add_index :account_users, [:account_id, :user_id], unique: true
    add_index :account_users, :role
    add_index :account_users, :active
    add_index :account_users, :joined_at
  end
end
