class CreateWaitlistEntries < ActiveRecord::Migration[8.0]
  def change
    create_table :waitlist_entries do |t|
      t.references :project, null: false, foreign_key: true
      t.references :account, null: false, foreign_key: true
      t.string :email, null: false
      t.string :name
      t.string :phone_number
      t.string :status, default: 'pending', null: false
      t.integer :position
      t.string :referral_code
      t.string :referred_by
      t.jsonb :custom_data, default: {}
      t.datetime :confirmed_at
      t.string :confirmation_token
      t.datetime :confirmation_sent_at
      t.string :ip_address
      t.string :user_agent
      t.string :source
      t.integer :referrals_count, default: 0
      t.boolean :email_verified, default: false
      t.datetime :notified_at

      t.timestamps
    end
    
    add_index :waitlist_entries, [:project_id, :email], unique: true
    add_index :waitlist_entries, :email
    add_index :waitlist_entries, :status
    add_index :waitlist_entries, :position
    add_index :waitlist_entries, :referral_code, unique: true
    add_index :waitlist_entries, :referred_by
    add_index :waitlist_entries, :confirmation_token, unique: true
    add_index :waitlist_entries, :confirmed_at
    add_index :waitlist_entries, :source
  end
end
