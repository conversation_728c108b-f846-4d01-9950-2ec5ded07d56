class CreateLandingPages < ActiveRecord::Migration[8.0]
  def change
    create_table :landing_pages do |t|
      t.references :project, null: false, foreign_key: true
      
      # Basic content
      t.string :title, null: false
      t.string :subtitle
      t.text :description
      t.string :hero_image_url
      
      # Design customization
      t.string :primary_color, default: '#3B82F6'
      t.string :secondary_color, default: '#6366F1'
      t.text :custom_css
      
      # Domain and publishing
      t.string :custom_domain
      t.boolean :published, default: false, null: false
      t.string :slug, null: false
      
      # SEO Meta Tags
      t.string :meta_title, limit: 60
      t.text :meta_description, limit: 160
      t.string :meta_keywords
      t.string :robots_meta, default: 'index, follow'
      t.string :canonical_url
      
      # Open Graph (Facebook, LinkedIn)
      t.string :og_title, limit: 60
      t.text :og_description, limit: 160
      t.string :og_image_url
      t.string :og_type, default: 'website'
      
      # Twitter Cards
      t.string :twitter_title, limit: 60
      t.text :twitter_description, limit: 160
      t.string :twitter_image_url
      t.string :twitter_card, default: 'summary_large_image'
      
      # Schema.org structured data
      t.text :schema_markup
      
      # SEO tracking
      t.integer :page_views, default: 0
      t.integer :unique_visitors, default: 0
      t.datetime :last_crawled_at
      
      t.timestamps
    end
    
    # Indexes for performance and SEO
    add_index :landing_pages, :slug, unique: true
    add_index :landing_pages, :custom_domain, unique: true
    add_index :landing_pages, :published
    add_index :landing_pages, [:project_id, :published]
    add_index :landing_pages, :created_at
    add_index :landing_pages, :page_views
  end
end
