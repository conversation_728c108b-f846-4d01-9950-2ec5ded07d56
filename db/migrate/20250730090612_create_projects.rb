class CreateProjects < ActiveRecord::Migration[8.0]
  def change
    create_table :projects do |t|
      t.references :account, null: false, foreign_key: true
      t.string :name, null: false
      t.string :slug, null: false
      t.text :description
      t.string :status, default: 'draft', null: false
      t.jsonb :settings, default: {}
      t.jsonb :custom_fields, default: []
      t.jsonb :branding, default: {}
      t.jsonb :analytics, default: {}
      t.string :website_url
      t.string :logo_url
      t.string :custom_domain
      t.boolean :public_stats, default: true
      t.boolean :email_collection_enabled, default: true
      t.boolean :double_opt_in, default: true
      t.datetime :launched_at
      t.datetime :archived_at

      t.timestamps
    end
    
    add_index :projects, :slug, unique: true
    add_index :projects, [:account_id, :name]
    add_index :projects, :status
    add_index :projects, :custom_domain
    add_index :projects, :launched_at
    add_index :projects, :public_stats
  end
end
