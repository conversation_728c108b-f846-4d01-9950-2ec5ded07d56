# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

# Create test data for development
if Rails.env.development?
  # Create a test user
  user = User.find_or_create_by!(email: '<EMAIL>') do |u|
    u.password = 'password123'
    u.password_confirmation = 'password123'
    u.first_name = 'Test'
    u.last_name = 'User'
  end
  
  # Create an account
  account = Account.find_or_create_by!(name: 'Test Company') do |a|
    a.subdomain = 'test-company'
    a.owner = user
  end
  
  # Create a project
  project = Project.find_or_create_by!(account: account, name: 'Amazing Product Launch') do |p|
    p.slug = 'amazing-product-launch'
    p.description = 'Get ready for the most innovative product of the year!'
  end
  
  # Create a landing page
  landing_page = LandingPage.find_or_create_by!(project: project, title: 'Revolutionary AI Assistant') do |lp|
    lp.subtitle = 'The future of productivity is here'
    lp.description = 'Join thousands of professionals who are already transforming their workflow with our cutting-edge AI assistant. Be among the first to experience the next generation of productivity tools.'
    lp.slug = 'revolutionary-ai-assistant'
    lp.published = true
    lp.primary_color = '#3B82F6'
    lp.secondary_color = '#6366F1'
    lp.hero_image_url = 'https://images.unsplash.com/photo-*************-21780ecad995?auto=format&fit=crop&w=800&q=80'
    lp.meta_title = 'Revolutionary AI Assistant - Join the Waitlist'
    lp.meta_description = 'Transform your productivity with our cutting-edge AI assistant. Join the exclusive waitlist now.'
  end
  
  puts "✅ Created test data:"
  puts "   User: #{user.email} (password: password123)"
  puts "   Account: #{account.name}"  
  puts "   Project: #{project.name}"
  puts "   Landing Page: #{landing_page.title}"
  puts "   🌐 View the landing page at: http://localhost:3000/page/#{landing_page.id}"
end
