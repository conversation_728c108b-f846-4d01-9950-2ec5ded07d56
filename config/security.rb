# Security Configuration for WaitlistBuilder
# Comprehensive security middleware and configuration setup

# Rate Limiting Configuration
require 'rack/attack'

class Rack::Attack
  # Configure Solid Cache for distributed rate limiting (Rails 8 Trifecta)
  if Rails.env.production?
    Rack::Attack.cache.store = Rails.cache
  end

  # Throttle all requests by IP (300 requests per 5 minutes)
  throttle('req/ip', limit: 300, period: 5.minutes) do |req|
    req.ip unless req.path.start_with?('/assets')
  end

  # Throttle login attempts by IP address
  throttle('login/ip', limit: 5, period: 20.seconds) do |req|
    if req.path == '/users/sign_in' && req.post?
      req.ip
    end
  end

  # Throttle login attempts by email address
  throttle('login/email', limit: 5, period: 20.seconds) do |req|
    if req.path == '/users/sign_in' && req.post?
      req.params['user']&.dig('email').presence
    end
  end

  # Throttle password reset requests
  throttle('password_reset/email', limit: 3, period: 1.hour) do |req|
    if req.path == '/users/password' && req.post?
      req.params['user']&.dig('email').presence
    end
  end

  # Throttle registration attempts by IP
  throttle('registration/ip', limit: 10, period: 1.hour) do |req|
    if req.path == '/users' && req.post?
      req.ip
    end
  end

  # Throttle waitlist signups per IP
  throttle('waitlist_signup/ip', limit: 5, period: 20.seconds) do |req|
    if req.path.match?(/.*\/join$/) && req.post?
      req.ip
    end
  end

  # Throttle waitlist signups per email
  throttle('waitlist_signup/email', limit: 3, period: 1.hour) do |req|
    if req.path.match?(/.*\/join$/) && req.post?
      req.params['email'].presence
    end
  end

  # Throttle API requests
  throttle('api/ip', limit: 100, period: 1.hour) do |req|
    if req.path.start_with?('/api/')
      req.ip
    end
  end

  # Block suspicious requests
  blocklist('block_bad_ua') do |req|
    # Block requests with suspicious user agents
    %w[
      badbot
      scrapy
      python-requests
      curl
      wget
    ].any? { |ua| req.user_agent&.downcase&.include?(ua) }
  end

  # Block requests from known bad IPs
  blocklist('block_bad_ips') do |req|
    # Add known malicious IPs here
    bad_ips = ENV.fetch('BLOCKED_IPS', '').split(',')
    bad_ips.include?(req.ip)
  end

  # Allow specific IPs to bypass rate limits (office IPs, monitoring services)
  safelist('allow_local') do |req|
    '127.0.0.1' == req.ip || '::1' == req.ip
  end

  # Allow monitoring services
  safelist('allow_monitoring') do |req|
    allowed_ips = ENV.fetch('MONITORING_IPS', '').split(',')
    allowed_ips.include?(req.ip)
  end

  # Custom response for throttled requests
  self.throttled_responder = lambda do |env|
    retry_after = (env['rack.attack.match_data'] || {})[:period]
    [
      429,
      {
        'Content-Type' => 'application/json',
        'Retry-After' => retry_after.to_s,
        'X-RateLimit-Limit' => env['rack.attack.matched'],
        'X-RateLimit-Remaining' => '0',
        'X-RateLimit-Reset' => (Time.now + retry_after).to_i.to_s
      },
      [{
        error: 'Rate limit exceeded',
        message: 'Too many requests. Please try again later.',
        retry_after: retry_after
      }.to_json]
    ]
  end

  # Custom response for blocked requests
  self.blocklisted_responder = lambda do |env|
    [
      403,
      { 'Content-Type' => 'application/json' },
      [{
        error: 'Forbidden',
        message: 'Your request has been blocked.'
      }.to_json]
    ]
  end
end

# Content Security Policy Configuration
Rails.application.configure do
  config.content_security_policy do |policy|
    policy.default_src :self, :https
    policy.font_src    :self, :https, :data
    policy.img_src     :self, :https, :data, 'blob:'
    policy.object_src  :none
    policy.script_src  :self, :https, :unsafe_inline
    policy.style_src   :self, :https, :unsafe_inline
    policy.connect_src :self, :https, 'wss:', 'ws:'
    policy.frame_src   :none
    policy.base_uri    :self
    policy.form_action :self
    
    # Allow specific external domains for integrations
    if Rails.env.production?
      policy.img_src :self, :https, :data, 'blob:', '*.cloudflare.com', '*.amazonaws.com'
      policy.connect_src :self, :https, 'wss:', 'ws:', '*.google-analytics.com'
    end
    
    # Report violations in production
    if Rails.env.production?
      policy.report_uri ENV.fetch('CSP_REPORT_URI', '/csp-violation-report')
    end
  end

  # Generate nonce for inline scripts
  config.content_security_policy_nonce_generator = -> request { SecureRandom.base64(16) }

  # Report CSP violations
  config.content_security_policy_report_only = false
end

# Force SSL in production
Rails.application.configure do
  if Rails.env.production?
    config.force_ssl = true
    config.ssl_options = {
      redirect: {
        exclude: -> request { request.path.start_with?('/health') }
      },
      secure_cookies: true,
      hsts: {
        max_age: 31_536_000, # 1 year
        include_subdomains: true,
        preload: true
      }
    }
  end
end

# Session Security Configuration
Rails.application.configure do
  config.session_store :cookie_store,
    key: '_waitlistbuilder_session',
    secure: Rails.env.production?,
    httponly: true,
    same_site: :lax,
    expire_after: 30.minutes
end

# Additional Security Headers Middleware
class SecurityHeadersMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    status, headers, response = @app.call(env)
    
    # Add security headers
    headers.merge!(security_headers)
    
    [status, headers, response]
  end

  private

  def security_headers
    {
      # Prevent clickjacking
      'X-Frame-Options' => 'DENY',
      
      # Prevent MIME type sniffing
      'X-Content-Type-Options' => 'nosniff',
      
      # Enable XSS protection
      'X-XSS-Protection' => '1; mode=block',
      
      # Control referrer information
      'Referrer-Policy' => 'strict-origin-when-cross-origin',
      
      # Permissions policy (formerly Feature-Policy)
      'Permissions-Policy' => [
        'geolocation=()',
        'microphone=()',
        'camera=()',
        'magnetometer=()',
        'gyroscope=()',
        'speaker=()',
        'vibrate=()',
        'fullscreen=(self)',
        'payment=()'
      ].join(', '),
      
      # Expect-CT header for certificate transparency
      'Expect-CT' => 'max-age=86400, enforce',
      
      # Server information hiding
      'Server' => 'WaitlistBuilder'
    }
  end
end

# CORS Configuration for API endpoints
Rails.application.configure do
  config.middleware.insert_before 0, Rack::Cors do
    allow do
      origins do |source, env|
        # Allow requests from user's custom domains
        request = ActionDispatch::Request.new(env)
        project_slug = extract_project_slug(request.path)
        
        if project_slug
          project = Project.find_by(slug: project_slug)
          allowed_origins = [
            project&.website_url,
            "https://#{project_slug}.waitlistbuilder.com"
          ].compact
          
          allowed_origins.include?(source)
        else
          false
        end
      end
      
      resource '/api/*',
        headers: :any,
        methods: [:get, :post, :put, :patch, :delete, :options, :head],
        credentials: false,
        max_age: 86400
    end
    
    # Allow specific origins for development
    if Rails.env.development?
      allow do
        origins 'localhost:3000', '127.0.0.1:3000', /\Ahttp:\/\/localhost:\d+\z/
        resource '*', headers: :any, methods: :any
      end
    end
  end
end

# Input Sanitization
class InputSanitizer
  def self.sanitize_html(html)
    return '' if html.blank?
    
    # Use ActionView's sanitize with restrictive whitelist
    ActionController::Base.helpers.sanitize(html, 
      tags: %w[p br strong em u i b ul ol li a h1 h2 h3 h4 h5 h6],
      attributes: %w[href title]
    )
  end
  
  def self.sanitize_text(text)
    return '' if text.blank?
    
    # Remove potential XSS patterns
    text.gsub(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi, '')
        .gsub(/javascript:/i, '')
        .gsub(/on\w+\s*=/i, '')
        .strip
  end
  
  def self.sanitize_email(email)
    return '' if email.blank?
    
    # Basic email sanitization
    email.downcase.strip.gsub(/[^\w@.-]/, '')
  end
end

# Database Security Configuration
ActiveRecord::Base.establish_connection.tap do |connection|
  # Set secure connection parameters
  if Rails.env.production?
    connection.execute("SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'")
    connection.execute("SET SESSION time_zone = '+00:00'")
  end
end

# Logging Security Configuration
Rails.application.configure do
  # Filter sensitive parameters from logs
  config.filter_parameters += [
    :password,
    :password_confirmation,
    :current_password,
    :token,
    :authenticity_token,
    :api_key,
    :secret,
    :private_key,
    :encrypted_password,
    :otp_secret_key,
    :ssn,
    :credit_card
  ]
  
  # Configure secure logging in production
  if Rails.env.production?
    config.log_level = :info
    config.log_formatter = proc do |severity, datetime, progname, msg|
      {
        timestamp: datetime.iso8601,
        level: severity,
        message: msg,
        process_id: Process.pid,
        thread_id: Thread.current.object_id,
        hostname: Socket.gethostname
      }.to_json + "\n"
    end
  end
end

# File Upload Security
Rails.application.configure do
  # Configure Active Storage security
  config.active_storage.variant_processor = :mini_magick
  config.active_storage.content_types_to_serve_as_binary = %w[
    text/html
    text/javascript
    image/svg+xml
    application/postscript
    application/x-shockwave-flash
    text/xml
    application/xml
    application/xhtml+xml
    text/css
  ]
  
  # File size limits
  config.active_storage.max_file_size = 10.megabytes
  
  # Allowed content types for uploads
  config.active_storage.allowed_image_types = %w[
    image/png
    image/jpeg
    image/gif
    image/webp
  ]
end

# API Security Configuration
module ApiSecurity
  def self.generate_api_key
    SecureRandom.urlsafe_base64(32)
  end
  
  def self.hash_api_key(key)
    Digest::SHA256.hexdigest("#{key}#{Rails.application.secret_key_base}")
  end
  
  def self.verify_api_key(provided_key, stored_hash)
    hash_api_key(provided_key) == stored_hash
  end
end

# Environment-specific security configurations
case Rails.env
when 'production'
  # Production security hardening
  Rails.application.configure do
    # Disable detailed error pages
    config.consider_all_requests_local = false
    config.action_controller.perform_caching = true
    
    # Enable Active Record encryption
    config.active_record.encryption.encrypt_fixtures = true
    config.active_record.encryption.store_key_references = true
    
    # Configure trusted proxies (for load balancers)
    config.force_ssl = true
    config.action_dispatch.trusted_proxies = /\A127\.0\.0\.1\Z|\A(10|172\.(1[6-9]|2[0-9]|30|31)|192\.168)\.|\A::1\Z|\Afd[0-9a-f]{2}:.+|\Alocalhost\Z|\Aunix\Z|\Aunix:/
  end
  
when 'development'
  # Development security (less restrictive but still secure)
  Rails.application.configure do
    config.web_console.permissions = '127.0.0.1'
    
    # Allow localhost for CORS
    config.hosts = nil
  end
  
when 'test'
  # Test environment security
  Rails.application.configure do
    # Disable security features that interfere with testing
    config.force_ssl = false
  end
end

# Security Monitoring and Alerting
class SecurityMonitor
  def self.log_security_event(event_type, details = {})
    Rails.logger.warn({
      security_event: event_type,
      timestamp: Time.current.iso8601,
      details: details,
      request_id: details[:request_id]
    }.to_json)
    
    # Send to monitoring service in production
    if Rails.env.production? && ENV['SECURITY_WEBHOOK_URL']
      SecurityAlertJob.perform_later(event_type, details)
    end
  end
  
  def self.track_failed_login(email, ip_address, user_agent)
    log_security_event('failed_login', {
      email: email,
      ip_address: ip_address,
      user_agent: user_agent
    })
  end
  
  def self.track_suspicious_activity(activity_type, details)
    log_security_event('suspicious_activity', {
      activity_type: activity_type,
      details: details
    })
  end
end

# Vulnerability Scanning Configuration
if Rails.env.development?
  # Brakeman configuration for security scanning
  # Run with: bundle exec brakeman
  # Config file: config/brakeman.yml
end

# Data Encryption Configuration
Rails.application.configure do
  if Rails.env.production?
    # Configure Active Record Encryption
    config.active_record.encryption.primary_key = ENV.fetch('ENCRYPTION_PRIMARY_KEY')
    config.active_record.encryption.deterministic_key = ENV.fetch('ENCRYPTION_DETERMINISTIC_KEY')
    config.active_record.encryption.key_derivation_salt = ENV.fetch('ENCRYPTION_KEY_DERIVATION_SALT')
    
    # Hash digest class for encryption
    config.active_record.encryption.hash_digest_class = OpenSSL::Digest::SHA256
  end
end