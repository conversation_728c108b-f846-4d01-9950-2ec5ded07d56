# Rails 8 Solid Trifecta Database Configuration
# Separate databases for Queue, Cache, and Cable to optimize performance

default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  timeout: 5000
  username: <%= ENV.fetch("DATABASE_USERNAME", "waitlistbuilder") %>
  password: <%= ENV.fetch("DATABASE_PASSWORD", "") %>
  host: <%= ENV.fetch("DATABASE_HOST", "localhost") %>

development:
  primary:
    <<: *default
    database: waitlistbuilder_development
    
  # Solid Queue database
  queue:
    <<: *default
    database: waitlistbuilder_queue_development
    migrations_paths: db/queue_migrate
    
  # Solid Cache database  
  cache:
    <<: *default
    database: waitlistbuilder_cache_development
    migrations_paths: db/cache_migrate
    
  # Solid Cable database
  cable:
    <<: *default
    database: waitlistbuilder_cable_development
    migrations_paths: db/cable_migrate
    
  # Separate analytics cache for better performance
  cache_analytics:
    <<: *default
    database: waitlistbuilder_cache_analytics_development
    migrations_paths: db/cache_migrate
    
  # Session cache database
  cache_sessions:
    <<: *default
    database: waitlistbuilder_cache_sessions_development
    migrations_paths: db/cache_migrate

test:
  primary:
    <<: *default
    database: waitlistbuilder_test<%= ENV['TEST_ENV_NUMBER'] %>
    
  queue:
    <<: *default
    database: waitlistbuilder_queue_test<%= ENV['TEST_ENV_NUMBER'] %>
    migrations_paths: db/queue_migrate
    
  cache:
    <<: *default
    database: waitlistbuilder_cache_test<%= ENV['TEST_ENV_NUMBER'] %>
    migrations_paths: db/cache_migrate
    
  cable:
    <<: *default
    database: waitlistbuilder_cable_test<%= ENV['TEST_ENV_NUMBER'] %>
    migrations_paths: db/cable_migrate
    
  cache_analytics:
    <<: *default
    database: waitlistbuilder_cache_analytics_test<%= ENV['TEST_ENV_NUMBER'] %>
    migrations_paths: db/cache_migrate
    
  cache_sessions:
    <<: *default
    database: waitlistbuilder_cache_sessions_test<%= ENV['TEST_ENV_NUMBER'] %>
    migrations_paths: db/cache_migrate

staging:
  primary:
    <<: *default
    database: waitlistbuilder_staging
    username: <%= ENV.fetch("DATABASE_USERNAME") %>
    password: <%= ENV.fetch("DATABASE_PASSWORD") %>
    host: <%= ENV.fetch("DATABASE_HOST") %>
    port: <%= ENV.fetch("DATABASE_PORT", 5432) %>
    
  queue:
    <<: *default
    database: waitlistbuilder_queue_staging
    username: <%= ENV.fetch("DATABASE_USERNAME") %>
    password: <%= ENV.fetch("DATABASE_PASSWORD") %>
    host: <%= ENV.fetch("DATABASE_HOST") %>
    port: <%= ENV.fetch("DATABASE_PORT", 5432) %>
    migrations_paths: db/queue_migrate
    
  cache:
    <<: *default
    database: waitlistbuilder_cache_staging
    username: <%= ENV.fetch("DATABASE_USERNAME") %>
    password: <%= ENV.fetch("DATABASE_PASSWORD") %>
    host: <%= ENV.fetch("DATABASE_HOST") %>
    port: <%= ENV.fetch("DATABASE_PORT", 5432) %>
    migrations_paths: db/cache_migrate
    
  cable:
    <<: *default
    database: waitlistbuilder_cable_staging
    username: <%= ENV.fetch("DATABASE_USERNAME") %>
    password: <%= ENV.fetch("DATABASE_PASSWORD") %>
    host: <%= ENV.fetch("DATABASE_HOST") %>
    port: <%= ENV.fetch("DATABASE_PORT", 5432) %>
    migrations_paths: db/cable_migrate
    
  cache_analytics:
    <<: *default
    database: waitlistbuilder_cache_analytics_staging
    username: <%= ENV.fetch("DATABASE_USERNAME") %>
    password: <%= ENV.fetch("DATABASE_PASSWORD") %>
    host: <%= ENV.fetch("DATABASE_HOST") %>
    port: <%= ENV.fetch("DATABASE_PORT", 5432) %>
    migrations_paths: db/cache_migrate

production:
  primary:
    <<: *default
    database: waitlistbuilder_production
    username: <%= ENV.fetch("DATABASE_USERNAME") %>
    password: <%= ENV.fetch("DATABASE_PASSWORD") %>
    host: <%= ENV.fetch("DATABASE_HOST") %>
    port: <%= ENV.fetch("DATABASE_PORT", 5432) %>
    pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 20 } %>
    
  queue:
    <<: *default
    database: waitlistbuilder_queue_production
    username: <%= ENV.fetch("QUEUE_DATABASE_USERNAME", ENV.fetch("DATABASE_USERNAME")) %>
    password: <%= ENV.fetch("QUEUE_DATABASE_PASSWORD", ENV.fetch("DATABASE_PASSWORD")) %>
    host: <%= ENV.fetch("QUEUE_DATABASE_HOST", ENV.fetch("DATABASE_HOST")) %>
    port: <%= ENV.fetch("QUEUE_DATABASE_PORT", ENV.fetch("DATABASE_PORT", 5432)) %>
    pool: <%= ENV.fetch("QUEUE_POOL_SIZE") { 10 } %>
    migrations_paths: db/queue_migrate
    
  cache:
    <<: *default
    database: waitlistbuilder_cache_production
    username: <%= ENV.fetch("CACHE_DATABASE_USERNAME", ENV.fetch("DATABASE_USERNAME")) %>
    password: <%= ENV.fetch("CACHE_DATABASE_PASSWORD", ENV.fetch("DATABASE_PASSWORD")) %>
    host: <%= ENV.fetch("CACHE_DATABASE_HOST", ENV.fetch("DATABASE_HOST")) %>
    port: <%= ENV.fetch("CACHE_DATABASE_PORT", ENV.fetch("DATABASE_PORT", 5432)) %>
    pool: <%= ENV.fetch("CACHE_POOL_SIZE") { 15 } %>
    migrations_paths: db/cache_migrate
    
  cable:
    <<: *default
    database: waitlistbuilder_cable_production
    username: <%= ENV.fetch("CABLE_DATABASE_USERNAME", ENV.fetch("DATABASE_USERNAME")) %>
    password: <%= ENV.fetch("CABLE_DATABASE_PASSWORD", ENV.fetch("DATABASE_PASSWORD")) %>  
    host: <%= ENV.fetch("CABLE_DATABASE_HOST", ENV.fetch("DATABASE_HOST")) %>
    port: <%= ENV.fetch("CABLE_DATABASE_PORT", ENV.fetch("DATABASE_PORT", 5432)) %>
    pool: <%= ENV.fetch("CABLE_POOL_SIZE") { 10 } %>
    migrations_paths: db/cable_migrate
    
  # High-performance analytics cache with separate database
  cache_analytics:
    <<: *default
    database: waitlistbuilder_cache_analytics_production
    username: <%= ENV.fetch("ANALYTICS_CACHE_DATABASE_USERNAME", ENV.fetch("DATABASE_USERNAME")) %>
    password: <%= ENV.fetch("ANALYTICS_CACHE_DATABASE_PASSWORD", ENV.fetch("DATABASE_PASSWORD")) %>
    host: <%= ENV.fetch("ANALYTICS_CACHE_DATABASE_HOST", ENV.fetch("DATABASE_HOST")) %>
    port: <%= ENV.fetch("ANALYTICS_CACHE_DATABASE_PORT", ENV.fetch("DATABASE_PORT", 5432)) %>
    pool: <%= ENV.fetch("ANALYTICS_CACHE_POOL_SIZE") { 8 } %>
    migrations_paths: db/cache_migrate
    
  # Dedicated session cache for scalability
  cache_sessions:
    <<: *default
    database: waitlistbuilder_cache_sessions_production
    username: <%= ENV.fetch("SESSION_CACHE_DATABASE_USERNAME", ENV.fetch("DATABASE_USERNAME")) %>
    password: <%= ENV.fetch("SESSION_CACHE_DATABASE_PASSWORD", ENV.fetch("DATABASE_PASSWORD")) %>
    host: <%= ENV.fetch("SESSION_CACHE_DATABASE_HOST", ENV.fetch("DATABASE_HOST")) %>
    port: <%= ENV.fetch("SESSION_CACHE_DATABASE_PORT", ENV.fetch("DATABASE_PORT", 5432)) %>
    pool: <%= ENV.fetch("SESSION_CACHE_POOL_SIZE") { 5 } %>
    migrations_paths: db/cache_migrate