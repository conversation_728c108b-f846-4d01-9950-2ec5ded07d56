# WaitlistBuilder Solid Queue Monitoring
# Rails 8 pre-configures Solid Queue - this adds custom monitoring

# Performance monitoring for Solid Queue
ActiveSupport::Notifications.subscribe 'solid_queue.job_completed' do |name, start, finish, id, payload|
  duration = finish - start
  job_class = payload[:job].class.name
  
  Rails.logger.info({
    event: 'solid_queue_job_completed',
    job_class: job_class,
    duration_ms: (duration * 1000).round(2),
    queue: payload[:job].queue_name,
    job_id: payload[:job].job_id
  }.to_json)
  
  # Alert on slow jobs (> 30 seconds)
  if duration > 30.seconds
    Rails.logger.warn({
      event: 'solid_queue_slow_job',
      job_class: job_class,
      duration_ms: (duration * 1000).round(2),
      job_id: payload[:job].job_id
    }.to_json)
  end
end

ActiveSupport::Notifications.subscribe 'solid_queue.job_failed' do |name, start, finish, id, payload|
  Rails.logger.error({
    event: 'solid_queue_job_failed',
    job_class: payload[:job].class.name,
    error: payload[:error].message,
    job_id: payload[:job].job_id,
    attempts: payload[:job].executions.count
  }.to_json)
end