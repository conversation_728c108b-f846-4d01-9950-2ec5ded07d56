# WaitlistBuilder Cache Helpers
# Rails 8 pre-configures Solid Cache - this adds custom cache utilities

# Cache key helpers for WaitlistBuilder
module WaitlistBuilder
  module C<PERSON><PERSON><PERSON>s
    def self.project_stats(project_id)
      "project:#{project_id}:stats:#{Date.current}"
    end
    
    def self.project_analytics(project_id, date_range)
      "project:#{project_id}:analytics:#{date_range.hash}"
    end
    
    def self.user_projects(user_id)
      "user:#{user_id}:projects"
    end
    
    def self.waitlist_entries(project_id, page = 1)
      "project:#{project_id}:entries:page:#{page}"
    end
    
    def self.project_public_data(slug)
      "project:#{slug}:public"
    end
    
    def self.email_template(template_id)
      "email_template:#{template_id}"
    end
    
    def self.analytics_funnel(project_id, date)
      "analytics:funnel:#{project_id}:#{date}"
    end
    
    def self.rate_limit(identifier, action)
      "rate_limit:#{action}:#{identifier}"
    end
  end
end

# Cache concerns for models
module Cacheable
  extend ActiveSupport::Concern
  
  included do
    after_commit :clear_cache, on: [:create, :update, :destroy]
  end
  
  class_methods do
    def cached_find(id, expires_in: 1.hour)
      Rails.cache.fetch("#{name.downcase}:#{id}", expires_in: expires_in) do
        find(id)
      end
    end
    
    def cached_where(conditions, expires_in: 30.minutes)
      cache_key = "#{name.downcase}:where:#{conditions.to_query}"
      Rails.cache.fetch(cache_key, expires_in: expires_in) do
        where(conditions).to_a
      end
    end
  end
  
  private
  
  def clear_cache
    Rails.cache.delete("#{self.class.name.downcase}:#{id}")
  end
end

# Performance monitoring for cache operations
if Rails.env.production?
  ActiveSupport::Notifications.subscribe 'cache_read.active_support' do |name, start, finish, id, payload|
    duration = finish - start
    
    # Log slow cache reads (> 100ms)
    if duration > 0.1
      Rails.logger.warn({
        event: 'slow_cache_read',
        key: payload[:key],
        duration_ms: (duration * 1000).round(2),
        hit: payload[:hit]
      }.to_json)
    end
  end
  
  ActiveSupport::Notifications.subscribe 'cache_write.active_support' do |name, start, finish, id, payload|
    duration = finish - start
    
    # Log slow cache writes (> 200ms)
    if duration > 0.2
      Rails.logger.warn({
        event: 'slow_cache_write',
        key: payload[:key],
        duration_ms: (duration * 1000).round(2)
      }.to_json)
    end
  end
end

# Cache warming strategies
module WaitlistBuilder
  class CacheWarmer
    def self.warm_project_cache(project)
      # Warm up project stats
      Rails.cache.fetch(CacheKeys.project_stats(project.id), expires_in: 1.hour) do
        {
          signup_count: project.waitlist_entries.confirmed.count,
          total_signups: project.waitlist_entries.count,
          conversion_rate: project.calculate_conversion_rate,
          recent_signups: project.waitlist_entries.recent.limit(10).to_a
        }
      end
      
      # Warm up public project data
      Rails.cache.fetch(CacheKeys.project_public_data(project.slug), expires_in: 6.hours) do
        project.as_json(
          only: [:id, :name, :slug, :description, :logo_url, :website_url, :status],
          include: {
            custom_fields: {
              only: [:id, :name, :label, :field_type, :required, :help_text, :options]
            }
          },
          methods: [:branding_colors, :social_links, :meta_tags]
        )
      end
    end
    
    def self.warm_analytics_cache(project, date_range = 7.days.ago..Time.current)
      cache_key = CacheKeys.project_analytics(project.id, date_range)
      
      Rails.cache.fetch(cache_key, expires_in: 1.hour) do
        AnalyticsService.new(project, date_range).generate_report
      end
    end
    
    def self.warm_user_cache(user)
      Rails.cache.fetch(CacheKeys.user_projects(user.id), expires_in: 30.minutes) do
        user.projects.includes(:custom_fields, :waitlist_entries).to_a
      end
    end
  end
end

# Background job will be defined in app/jobs/ directory after ApplicationJob is available
# Cache warming will be scheduled after application initialization

# Fragment caching helpers for views
module ApplicationHelper
  def cache_if(condition, name = {}, options = {}, &block)
    if condition
      cache(name, options, &block)
    else
      yield
    end
  end
  
  def cache_unless(condition, name = {}, options = {}, &block)
    cache_if(!condition, name, options, &block)
  end
end