# Load security configuration
require_relative '../security'

# Add security middleware to the stack
Rails.application.configure do
  # Add custom security headers middleware
  config.middleware.insert_before ActionDispatch::Static, SecurityHeadersMiddleware
  
  # Add Rack::Attack for rate limiting
  config.middleware.use Rack::Attack
  
  # Configure secure cookies
  config.session_store :cookie_store,
    key: '_waitlistbuilder_session',
    secure: Rails.env.production?,
    httponly: true,
    same_site: :lax,
    expire_after: 30.minutes
end

# Security event logging
ActiveSupport::Notifications.subscribe 'rack.attack' do |name, start, finish, request_id, payload|
  req = payload[:request]
  
  case req.env['rack.attack.match_discriminator']
  when 'req/ip'
    SecurityMonitor.log_security_event('rate_limit_exceeded', {
      ip_address: req.ip,
      path: req.path,
      user_agent: req.user_agent,
      request_id: request_id
    })
  when /login/
    SecurityMonitor.log_security_event('login_rate_limit_exceeded', {
      ip_address: req.ip,
      email: req.params.dig('user', 'email'),
      request_id: request_id
    })
  end
end

# Helper method to extract project slug from path
def extract_project_slug(path)
  # Extract slug from paths like /project-slug or /project-slug/join
  match = path.match(/\A\/([a-z0-9\-]+)(?:\/|$)/)
  match ? match[1] : nil
end