# ActsAsTenant Configuration for WaitlistBuilder
# Multi-tenant data scoping by Account

ActsAsTenant.configure do |config|
  # Require tenant to be set for all operations
  config.require_tenant = true
  
  # Allow current_tenant to be nil in console for development
  if Rails.env.development?
    config.require_tenant = false
  end
end

# Helper methods for ApplicationController
module ActsAsTenantHelpers
  extend ActiveSupport::Concern
  
  included do
    # Set current tenant based on subdomain or current user
    before_action :set_current_tenant
    
    private
    
    def set_current_tenant
      if user_signed_in?
        # For authenticated users, use their current account
        # This can be enhanced with session-based account switching
        account = current_user.accounts.first
        ActsAsTenant.current_tenant = account
      elsif request.subdomain.present? && request.subdomain != 'www'
        # For public pages, use subdomain to determine tenant
        account = Account.find_by(subdomain: request.subdomain)
        ActsAsTenant.current_tenant = account if account
      end
    end
    
    def current_account
      ActsAsTenant.current_tenant
    end
    
    def require_account!
      redirect_to root_path unless current_account
    end
  end
end