# Devise Security Configuration
Devise.setup do |config|
  # Security Configurations
  config.password_length = 12..128
  config.email_regexp = /\A[^@\s]+@[^@\s]+\.[^@\s]+\z/
  config.case_insensitive_keys = [:email]
  config.strip_whitespace_keys = [:email]
  
  # Account Lockout Configuration
  config.lock_strategy = :failed_attempts
  config.unlock_strategy = :email
  config.maximum_attempts = 5
  config.unlock_in = 1.hour
  config.reset_password_within = 6.hours
  
  # Session Security
  config.expire_all_remember_me_on_sign_out = true
  config.remember_for = 2.weeks
  config.timeout_in = 30.minutes
  
  # Email Confirmation
  config.confirm_within = 3.days
  config.confirmation_keys = [:email]
  config.reconfirmable = true
  
  # Password Security  
  config.stretches = Rails.env.test? ? 1 : 12
  
  # Paranoid mode (don't reveal if email exists)
  config.paranoid = true
  
  # Sign out via delete method only
  config.sign_out_via = :delete
  
  # Navigation format
  config.navigational_formats = ['*/*', :html]
  
  # HTTP authentication realm
  config.http_authentication_realm = 'WaitlistBuilder'
  
  # Warden configuration
  config.warden do |manager|
    manager.failure_app = CustomDeviseFailure
    manager.default_scope = :user
    manager.intercept_401 = false
  end
end

# Custom Devise failure app for API responses
class CustomDeviseFailure < Devise::FailureApp
  def respond
    if api_request?
      json_failure
    else
      super
    end
  end

  private

  def json_failure
    self.status = 401
    self.content_type = 'application/json'
    self.response_body = {
      error: 'Authentication failed',
      message: i18n_message,
      code: 'UNAUTHORIZED'
    }.to_json
  end

  def api_request?
    request.path.start_with?('/api/') || 
    request.headers['Accept']&.include?('application/json')
  end
end

# Password strength validator
class PasswordStrengthValidator < ActiveModel::EachValidator
  def validate_each(record, attribute, value)
    return if value.blank?
    
    errors = []
    
    # Minimum length (handled by Devise, but double-check)
    errors << "must be at least 12 characters long" if value.length < 12
    
    # Must contain uppercase letter
    errors << "must contain at least one uppercase letter" unless value.match?(/[A-Z]/)
    
    # Must contain lowercase letter  
    errors << "must contain at least one lowercase letter" unless value.match?(/[a-z]/)
    
    # Must contain number
    errors << "must contain at least one number" unless value.match?(/\d/)
    
    # Must contain special character
    errors << "must contain at least one special character (!@#$%^&*)" unless value.match?(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/)
    
    # Check against common passwords
    errors << "is too common - please choose a more unique password" if common_password?(value)
    
    # Check for sequential characters
    errors << "cannot contain sequential characters (e.g., 123, abc)" if sequential_characters?(value)
    
    # Check for repeated characters
    errors << "cannot contain more than 2 repeated characters in a row" if repeated_characters?(value)
    
    errors.each { |error| record.errors.add(attribute, error) }
  end
  
  private
  
  def common_password?(password)
    common_passwords = %w[
      password123 123456789 qwerty123 password1 welcome123
      admin123 letmein123 monkey123 dragon123 sunshine123
      princess123 starwars123 football123 baseball123 superman123
    ]
    
    common_passwords.any? { |common| password.downcase.include?(common) }
  end
  
  def sequential_characters?(password)
    # Check for 3+ sequential numbers or letters
    password.match?(/(?:012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i)
  end
  
  def repeated_characters?(password)
    # Check for 3+ repeated characters
    password.match?(/(.)\1{2,}/)
  end
end

# Track authentication events
Warden::Manager.after_authentication do |user, auth, opts|
  SecurityMonitor.log_security_event('successful_login', {
    user_id: user.id,
    email: user.email,
    ip_address: auth.request.remote_ip,
    user_agent: auth.request.user_agent
  })
end

Warden::Manager.after_failed_fetch do |user, auth, opts|
  SecurityMonitor.log_security_event('failed_authentication', {
    scope: opts[:scope],
    ip_address: auth.request.remote_ip,
    user_agent: auth.request.user_agent,
    path: auth.request.path
  })
end

# Configure Devise modules
Devise.add_module(:password_strength, {
  strategy: true,
  model: 'devise/models/password_strength'
})

# Additional security configuration
Rails.application.config.to_prepare do
  # Customize Devise controllers for additional security
  Devise::SessionsController.class_eval do
    before_action :configure_sign_in_params, only: [:create]
    after_action :track_sign_in_attempt, only: [:create]
    
    private
    
    def configure_sign_in_params
      devise_parameter_sanitizer.permit(:sign_in, keys: [:email, :password, :remember_me])
    end
    
    def track_sign_in_attempt
      if user_signed_in?
        SecurityMonitor.log_security_event('successful_sign_in', {
          user_id: current_user.id,
          email: current_user.email,
          ip_address: request.remote_ip,
          user_agent: request.user_agent
        })
      else
        SecurityMonitor.track_failed_login(
          params.dig(:user, :email),
          request.remote_ip,
          request.user_agent
        )
      end
    end
  end
  
  Devise::RegistrationsController.class_eval do
    before_action :configure_sign_up_params, only: [:create]
    before_action :configure_account_update_params, only: [:update]
    after_action :track_registration_attempt, only: [:create]
    
    private
    
    def configure_sign_up_params
      devise_parameter_sanitizer.permit(:sign_up, keys: [:name, :email, :password, :password_confirmation])
    end
    
    def configure_account_update_params
      devise_parameter_sanitizer.permit(:account_update, keys: [:name, :email, :password, :password_confirmation, :current_password])
    end
    
    def track_registration_attempt
      SecurityMonitor.log_security_event('registration_attempt', {
        email: params.dig(:user, :email),
        success: resource.persisted?,
        errors: resource.errors.full_messages,
        ip_address: request.remote_ip,
        user_agent: request.user_agent
      })
    end
  end
  
  Devise::PasswordsController.class_eval do
    after_action :track_password_reset_request, only: [:create]
    
    private
    
    def track_password_reset_request
      SecurityMonitor.log_security_event('password_reset_request', {
        email: params.dig(:user, :email),
        ip_address: request.remote_ip,
        user_agent: request.user_agent
      })
    end
  end
end