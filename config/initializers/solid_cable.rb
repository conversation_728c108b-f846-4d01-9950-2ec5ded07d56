# WaitlistBuilder Action Cable Configuration
# Rails 8 pre-configures Solid Cable - this adds custom settings

Rails.application.configure do
  # Allow WebSocket connections from waitlistbuilder domains
  config.action_cable.allowed_request_origins = [
    /http:\/\/localhost:\d+/,
    /https:\/\/.*\.waitlistbuilder\.com/,
    /https:\/\/waitlistbuilder\.com/
  ] if Rails.env.production?
end

# WebSocket channels and connections will be defined in app/channels/ directory

# Broadcasting service for real-time updates
module WaitlistBuilder
  class Broadcaster
    def self.project_stats_updated(project)
      stats = {
        signup_count: project.waitlist_entries.confirmed.count,
        total_signups: project.waitlist_entries.count,
        conversion_rate: project.calculate_conversion_rate.round(2),
        updated_at: Time.current.iso8601
      }
      
      # Broadcast to project dashboard
      Channels::ProjectDashboardChannel.broadcast_to(
        project,
        {
          type: 'stats_updated',
          data: stats
        }
      )
      
      # Broadcast to public waitlist page
      ActionCable.server.broadcast(
        "waitlist:#{project.slug}",
        {
          type: 'stats_updated',
          data: stats.except(:conversion_rate) # Don't expose conversion rate publicly
        }
      )
    end
    
    def self.new_signup(project, waitlist_entry)
      # Broadcast to project dashboard
      Channels::ProjectDashboardChannel.broadcast_to(
        project,
        {
          type: 'new_signup',
          data: {
            id: waitlist_entry.id,
            email: waitlist_entry.email,
            name: waitlist_entry.name,
            status: waitlist_entry.status,
            queue_position: waitlist_entry.queue_position,
            created_at: waitlist_entry.created_at.iso8601
          }
        }
      )
      
      # Broadcast live counter update to public page
      ActionCable.server.broadcast(
        "waitlist:#{project.slug}",
        {
          type: 'signup_count_updated',
          data: {
            signup_count: project.waitlist_entries.confirmed.count,
            total_signups: project.waitlist_entries.count
          }
        }
      )
    end
    
    def self.analytics_updated(project, analytics_data)
      ActionCable.server.broadcast(
        "analytics:#{project.id}",
        {
          type: 'analytics_updated',
          data: analytics_data,
          updated_at: Time.current.iso8601
        }
      )
    end
    
    def self.user_notification(user, notification)
      Channels::NotificationsChannel.broadcast_to(
        user,
        {
          type: 'notification',
          data: {
            id: notification.id,
            title: notification.title,
            message: notification.message,
            notification_type: notification.notification_type,
            created_at: notification.created_at.iso8601
          }
        }
      )
    end
    
    def self.milestone_reached(project, milestone)
      # Broadcast to dashboard
      Channels::ProjectDashboardChannel.broadcast_to(
        project,
        {
          type: 'milestone_reached',
          data: {
            milestone_type: milestone[:type],
            value: milestone[:value],
            message: "🎉 #{milestone[:message]}"
          }
        }
      )
      
      # Broadcast celebration to public page
      ActionCable.server.broadcast(
        "waitlist:#{project.slug}",
        {
          type: 'milestone_celebration',
          data: {
            message: milestone[:public_message] || "We just hit a major milestone!",
            signup_count: project.waitlist_entries.confirmed.count
          }
        }
      )
    end
  end
end

# Background job will be defined in app/jobs/ directory after ApplicationJob is available

# Model callbacks to trigger real-time updates
module WaitlistBuilder
  module Concerns
    module Broadcastable
      extend ActiveSupport::Concern
      
      included do
        after_create_commit :broadcast_creation
        after_update_commit :broadcast_update
        after_destroy_commit :broadcast_destruction
      end
      
      private
      
      def broadcast_creation
        # Override in models to implement specific broadcast logic
      end
      
      def broadcast_update
        # Override in models to implement specific broadcast logic
      end
      
      def broadcast_destruction
        # Override in models to implement specific broadcast logic
      end
    end
  end
end

# JavaScript helpers for client-side WebSocket connections
# This would be used in the view templates
module WaitlistBuilder
  module WebSocketHelpers
    def websocket_meta_tags(project: nil, user: nil)
      tags = {
        'websocket-url' => action_cable_url,
        'websocket-enabled' => 'true'
      }
      
      if project
        tags['websocket-project-slug'] = project.slug
        tags['websocket-project-id'] = project.id if user_signed_in?
      end
      
      if user_signed_in?
        token = JWT.encode(
          { user_id: current_user.id, exp: 24.hours.from_now.to_i },
          Rails.application.credentials.secret_key_base,
          'HS256'
        )
        tags['websocket-token'] = token
      end
      
      tag_string = tags.map { |name, content| 
        tag.meta(name: name, content: content) 
      }.join("\n").html_safe
      
      tag_string
    end
  end
end

# Performance monitoring for WebSocket connections
ActiveSupport::Notifications.subscribe 'action_cable.broadcast' do |name, start, finish, id, payload|
  duration = finish - start
  
  Rails.logger.info({
    event: 'websocket_broadcast',
    channel: payload[:broadcasting_to],
    duration_ms: (duration * 1000).round(2),
    message_size: payload[:message].to_json.bytesize
  }.to_json)
end

ActiveSupport::Notifications.subscribe 'action_cable.connect' do |name, start, finish, id, payload|
  Rails.logger.info({
    event: 'websocket_connected',
    connection_id: payload[:connection].connection_identifier
  }.to_json)
end

ActiveSupport::Notifications.subscribe 'action_cable.disconnect' do |name, start, finish, id, payload|
  Rails.logger.info({
    event: 'websocket_disconnected',
    connection_id: payload[:connection].connection_identifier
  }.to_json)
end