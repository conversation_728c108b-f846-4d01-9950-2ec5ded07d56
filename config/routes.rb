Rails.application.routes.draw do
  devise_for :users
  
  # Health check
  get "up" => "rails/health#show", as: :rails_health_check

  # Admin routes (authenticated)
  resources :projects do
    resources :landing_pages do
      member do
        get :preview
        post :duplicate
        patch :toggle_published
      end
      resources :waitlist_entries, except: [:new, :edit, :create, :update]
    end
  end
  
  # Public landing page routes (no authentication)
  get "page/:id", to: "landing_pages#public_show", as: :public_landing_page
  post "page/:landing_page_id/waitlist_entries", to: "waitlist_entries#create", as: :public_waitlist_entries
  get "confirm/:id/:token", to: "waitlist_entries#confirm", as: :confirm_waitlist_entry
  get "unsubscribe/:id/:token", to: "waitlist_entries#unsubscribe", as: :unsubscribe_waitlist_entry
  get "sitemap.xml", to: "landing_pages#sitemap", defaults: { format: 'xml' }
  get "robots.txt", to: "landing_pages#robots", defaults: { format: 'text' }
  
  # Custom domain support (for later implementation)
  # constraints CustomDomainConstraint.new do
  #   get "/*path", to: "landing_pages#public_show"
  #   root to: "landing_pages#public_show"
  # end

  # Defines the root path route ("/")
  root "home#index"
end
