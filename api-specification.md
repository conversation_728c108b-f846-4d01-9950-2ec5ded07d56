# WaitlistBuilder: API Specification

## Overview

This document defines the complete API specification for WaitlistBuilder, including public endpoints for waitlist interactions, private endpoints for user management, webhook systems, and integration capabilities.

---

## API Architecture

### Base URLs
- **Production**: `https://api.waitlistbuilder.com`
- **Staging**: `https://api.staging.waitlistbuilder.com` 
- **Development**: `http://localhost:3000`

### API Versioning
- Current Version: `v1`
- URL Pattern: `/api/v1/{endpoint}`
- Version Header: `Accept: application/vnd.waitlistbuilder.v1+json`

### Authentication Methods
1. **API Keys** (for server-to-server): `Authorization: Bearer {api_key}`
2. **JWT Tokens** (for client applications): `Authorization: Bearer {jwt_token}`
3. **Public Access** (for waitlist pages): No authentication required

---

## Public API Endpoints

### 1. Project Information

#### GET /projects/{slug}
**Purpose**: Retrieve public project information for waitlist pages

**Parameters**:
- `slug` (path, required): Project URL slug

**Response** (200 OK):
```json
{
  "id": "uuid",
  "name": "ProjectName",
  "slug": "project-slug", 
  "description": "Project description text",
  "logo_url": "https://example.com/logo.png",
  "website_url": "https://example.com",
  "status": "active",
  "branding_colors": {
    "primary": "#8B5CF6",
    "secondary": "#EC4899"
  },
  "social_links": {
    "twitter": "https://twitter.com/project",
    "linkedin": "https://linkedin.com/company/project"
  },
  "custom_fields": [
    {
      "id": "uuid",
      "name": "company",
      "label": "Company Name",
      "field_type": "text",
      "required": false,
      "help_text": "Optional company information",
      "options": []
    }
  ],
  "stats": {
    "signup_count": 1250,
    "progress_percentage": 62.5,
    "launch_threshold": 2000
  },
  "meta": {
    "title": "ProjectName - Join the Waitlist",
    "description": "Get early access to ProjectName",
    "og_image_url": "https://example.com/og-image.png"
  }
}
```

**Error Responses**:
- `404 Not Found`: Project not found or not active
- `429 Too Many Requests`: Rate limit exceeded

---

### 2. Waitlist Signup

#### POST /projects/{slug}/join
**Purpose**: Add new subscriber to project waitlist

**Parameters**:
- `slug` (path, required): Project URL slug

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "company": "Acme Corp",
  "message": "Looking forward to launch!",
  "custom_fields": {
    "field_uuid_1": "Marketing",
    "field_uuid_2": "Enterprise"
  },
  "utm_source": "twitter",
  "utm_medium": "social",
  "utm_campaign": "launch_announcement",
  "referral_code": "ABC123XYZ"
}
```

**Response** (201 Created):
```json
{
  "id": "uuid",
  "email": "<EMAIL>", 
  "status": "pending",
  "queue_position": 1251,
  "confirmation_required": true,
  "referral_code": "DEF456GHI",
  "referral_url": "https://project-slug.waitlistbuilder.com?ref=DEF456GHI",
  "created_at": "2025-07-30T14:30:00Z",
  "message": "Successfully joined the waitlist! Please check your email to confirm."
}
```

**Error Responses**:
- `400 Bad Request`: Invalid input data
- `422 Unprocessable Entity`: Validation errors
- `409 Conflict`: Email already exists for this project
- `429 Too Many Requests`: Rate limit exceeded

**Validation Errors** (422):
```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "email",
      "message": "Email format is invalid"
    },
    {
      "field": "custom_fields.field_uuid_1", 
      "message": "This field is required"
    }
  ]
}
```

---

### 3. Email Confirmation

#### GET /projects/{slug}/confirm/{token}
**Purpose**: Confirm waitlist signup via email link

**Parameters**:
- `slug` (path, required): Project URL slug
- `token` (path, required): Confirmation token from email

**Response** (200 OK):
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "status": "confirmed", 
  "queue_position": 1248,
  "confirmed_at": "2025-07-30T14:35:00Z",
  "message": "Email confirmed! You're officially on the waitlist."
}
```

**Error Responses**:
- `404 Not Found`: Invalid or expired token
- `410 Gone`: Already confirmed or unsubscribed

---

### 4. Analytics Tracking

#### POST /analytics/events
**Purpose**: Track user interactions and behavior

**Request Body**:
```json
{
  "project_slug": "project-slug",
  "event_type": "page_view",
  "session_id": "session_uuid", 
  "metadata": {
    "page_url": "https://project-slug.waitlistbuilder.com",
    "scroll_depth": 75,
    "time_on_page": 120
  },
  "utm_source": "twitter",
  "utm_medium": "social",
  "utm_campaign": "launch_announcement"
}
```

**Response** (202 Accepted):
```json
{
  "status": "accepted",
  "event_id": "uuid"
}
```

**Event Types**:
- `page_view`: Page loaded
- `form_view`: Signup form displayed
- `form_focus`: User interacted with form
- `form_submit_attempt`: Form submission started
- `form_submit_success`: Form submitted successfully
- `email_opened`: Email opened (via tracking pixel)
- `email_clicked`: Email link clicked
- `social_share`: Social sharing action
- `referral_link_clicked`: Referral link used

---

## Private API Endpoints (Authenticated)

### Authentication
All private endpoints require authentication via API key or JWT token:
```
Authorization: Bearer {token}
```

### 1. User Management

#### GET /user/profile
**Purpose**: Get current user profile information

**Response** (200 OK):
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "name": "John Doe", 
  "company": "Acme Corp",
  "subscription_tier": "growth",
  "subscription_expires_at": "2025-12-31T23:59:59Z",
  "usage": {
    "projects_count": 3,
    "projects_limit": 10,
    "total_signups": 2847,
    "signups_limit": 5000
  },
  "created_at": "2025-01-15T10:00:00Z"
}
```

#### PATCH /user/profile
**Purpose**: Update user profile information

**Request Body**:
```json
{
  "name": "John Smith",
  "company": "New Company Inc",
  "email_notifications": false,
  "timezone": "America/New_York"
}
```

**Response** (200 OK):
```json
{
  "id": "uuid", 
  "message": "Profile updated successfully"
}
```

---

### 2. Project Management

#### GET /projects
**Purpose**: List user's projects with pagination

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (draft, active, paused, closed, archived)
- `sort` (optional): Sort order (created_at, name, signup_count)

**Response** (200 OK):
```json
{
  "projects": [
    {
      "id": "uuid",
      "name": "Project Alpha",
      "slug": "project-alpha",
      "status": "active", 
      "signup_count": 1250,
      "conversion_rate": 15.2,
      "created_at": "2025-06-01T10:00:00Z",
      "updated_at": "2025-07-30T14:00:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 3,
    "total_count": 42,
    "per_page": 20
  }
}
```

#### POST /projects
**Purpose**: Create new project

**Request Body**:
```json
{
  "name": "New Project", 
  "description": "Project description here",
  "website_url": "https://example.com",
  "logo_url": "https://example.com/logo.png",
  "launch_threshold": 500,
  "requires_confirmation": true,
  "collect_names": true,
  "collect_companies": false,
  "branding_colors": {
    "primary": "#8B5CF6",
    "secondary": "#EC4899"
  }
}
```

**Response** (201 Created):
```json
{
  "id": "uuid",
  "slug": "new-project",
  "message": "Project created successfully"
}
```

#### GET /projects/{id}
**Purpose**: Get detailed project information

**Response** (200 OK):
```json
{
  "id": "uuid",
  "name": "Project Alpha",
  "slug": "project-alpha", 
  "description": "Project description",
  "status": "active",
  "signup_count": 1250,
  "conversion_rate": 15.2,
  "custom_fields": [...],
  "email_sequences": [...],
  "recent_signups": [...],
  "analytics_summary": {
    "page_views_7d": 8234,
    "signups_7d": 127,
    "conversion_rate_7d": 15.4,
    "top_traffic_sources": [...]
  }
}
```

#### PATCH /projects/{id}
**Purpose**: Update project configuration

**Request Body**: Same as POST /projects

#### DELETE /projects/{id}
**Purpose**: Delete project (soft delete with 30-day recovery)

**Response** (204 No Content)**

---

### 3. Waitlist Management

#### GET /projects/{id}/entries
**Purpose**: List project's waitlist entries

**Query Parameters**:
- `page`, `per_page`: Pagination
- `status`: Filter by status (pending, confirmed, unsubscribed, bounced)
- `search`: Search by email or name
- `sort`: Sort by created_at, name, queue_position

**Response** (200 OK):
```json
{
  "entries": [
    {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "John Doe",
      "company": "Acme Corp", 
      "status": "confirmed",
      "queue_position": 45,
      "created_at": "2025-07-25T10:00:00Z",
      "confirmed_at": "2025-07-25T10:05:00Z",
      "utm_source": "twitter",
      "referral_count": 3,
      "custom_fields": {
        "company_size": "51-100",
        "use_case": "Marketing automation"
      }
    }
  ],
  "pagination": {...}
}
```

#### GET /projects/{id}/entries/{entry_id}
**Purpose**: Get detailed entry information

#### DELETE /projects/{id}/entries/{entry_id}
**Purpose**: Remove entry from waitlist

#### POST /projects/{id}/entries/{entry_id}/resend-confirmation
**Purpose**: Resend confirmation email

---

### 4. Analytics API

#### GET /projects/{id}/analytics
**Purpose**: Get comprehensive project analytics

**Query Parameters**:
- `start_date`: Start date (ISO 8601)
- `end_date`: End date (ISO 8601) 
- `granularity`: Data granularity (hour, day, week, month)

**Response** (200 OK):
```json
{
  "date_range": {
    "start_date": "2025-07-01T00:00:00Z",
    "end_date": "2025-07-30T23:59:59Z"
  },
  "overview": {
    "page_views": 12450,
    "unique_visitors": 8234,
    "signups": 1250,
    "confirmations": 1187,
    "conversion_rate": 9.53
  },
  "funnel": {
    "page_views": 12450,
    "form_views": 6225,
    "form_attempts": 1450,
    "form_successes": 1250,
    "confirmations": 1187,
    "page_to_form_rate": 50.0,
    "form_to_attempt_rate": 23.3,
    "attempt_to_success_rate": 86.2,
    "success_to_confirmation_rate": 95.0
  },
  "traffic_sources": {
    "Direct": 4523,
    "Twitter / Social": 3201,
    "Google / Organic": 2847,
    "LinkedIn / Social": 1879
  },
  "geographic": {
    "US": 6225,
    "CA": 1879, 
    "GB": 1456,
    "DE": 987
  },
  "trends": {
    "daily_signups": {
      "2025-07-01": 42,
      "2025-07-02": 38,
      "...": "..."
    },
    "hourly_views": {
      "00": 234,
      "01": 156,
      "...": "..."
    }
  },
  "devices": {
    "desktop": 7470,
    "mobile": 4158,
    "tablet": 822
  },
  "referrals": {
    "total_referral_signups": 187,
    "average_referrals_per_user": 2.3,
    "top_referrers": [...]
  }
}
```

#### GET /projects/{id}/analytics/export
**Purpose**: Export analytics data

**Query Parameters**:
- `format`: Export format (csv, json, xlsx)
- `start_date`, `end_date`: Date range

**Response**: File download with appropriate Content-Type

---

### 5. Email Management

#### GET /projects/{id}/email-sequences
**Purpose**: List email sequences for project

#### POST /projects/{id}/email-sequences
**Purpose**: Create new email sequence

**Request Body**:
```json
{
  "name": "Welcome Series",
  "description": "Welcome new subscribers",
  "trigger_event": "confirmed",
  "status": "active",
  "targeting_rules": [
    {
      "field": "status",
      "operator": "equals", 
      "value": "confirmed"
    }
  ]
}
```

#### GET /email-sequences/{id}/templates
**Purpose**: List email templates in sequence

#### POST /email-sequences/{id}/templates
**Purpose**: Create new email template

**Request Body**:
```json
{
  "subject": "Welcome to {{project_name}}!",
  "body": "<html>Welcome {{name}}! You're #{{position}} in line...</html>",
  "plain_text_body": "Welcome {{name}}! You're #{{position}} in line...",
  "sequence_position": 1,
  "delay_days": 0,
  "delay_hours": 0,
  "send_time_preferences": {
    "hour": 9,
    "timezone": "America/New_York"
  }
}
```

---

## Webhook System

### Webhook Events
WaitlistBuilder can send webhooks for the following events:

#### waitlist.signup_created
```json
{
  "event": "waitlist.signup_created",
  "timestamp": "2025-07-30T14:30:00Z",
  "data": {
    "project": {
      "id": "uuid",
      "name": "Project Alpha", 
      "slug": "project-alpha"
    },
    "entry": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "John Doe",
      "status": "pending",
      "queue_position": 1251,
      "custom_fields": {...},
      "utm_data": {...}
    }
  }
}
```

#### waitlist.signup_confirmed
```json
{
  "event": "waitlist.signup_confirmed",
  "timestamp": "2025-07-30T14:35:00Z", 
  "data": {
    "project": {...},
    "entry": {
      "id": "uuid",
      "email": "<EMAIL>",
      "status": "confirmed",
      "queue_position": 1248,
      "confirmed_at": "2025-07-30T14:35:00Z"
    }
  }
}
```

#### waitlist.milestone_reached
```json
{
  "event": "waitlist.milestone_reached",
  "timestamp": "2025-07-30T15:00:00Z",
  "data": {
    "project": {...},
    "milestone": {
      "type": "signup_count",
      "value": 1000,
      "current_count": 1000
    }
  }
}
```

#### email.delivered, email.opened, email.clicked, email.bounced
```json
{
  "event": "email.opened",
  "timestamp": "2025-07-30T16:00:00Z",
  "data": {
    "project": {...},
    "entry": {...},
    "email": {
      "template_id": "uuid",
      "subject": "Welcome to Project Alpha!",  
      "delivery_id": "uuid"
    }
  }
}
```

### Webhook Configuration

#### POST /webhooks
**Purpose**: Create webhook endpoint

**Request Body**:
```json
{
  "url": "https://yourapp.com/webhooks/waitlistbuilder",
  "events": ["waitlist.signup_created", "waitlist.signup_confirmed"],
  "active": true,
  "secret": "your_webhook_secret"
}
```

#### GET /webhooks
**Purpose**: List webhook endpoints

#### GET /webhooks/{id}/deliveries
**Purpose**: View webhook delivery history

---

## Rate Limiting

### Rate Limits by Endpoint Type

#### Public Endpoints
- **Waitlist Signup**: 5 requests per 20 seconds per IP
- **Analytics Tracking**: 100 requests per minute per IP
- **Project Info**: 60 requests per minute per IP

#### Private Endpoints  
- **API Key Authentication**: 1000 requests per hour
- **JWT Authentication**: 100 requests per hour
- **Bulk Operations**: 10 requests per minute

### Rate Limit Headers
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45  
X-RateLimit-Reset: 1627834800
Retry-After: 3600
```

### Rate Limit Response (429)
```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Try again in 60 seconds.",
  "retry_after": 60
}
```

---

## Error Handling

### Standard Error Response Format
```json
{
  "error": "Error Type",
  "message": "Human readable error message", 
  "details": {
    "field_errors": {...},
    "code": "ERROR_CODE",
    "timestamp": "2025-07-30T14:30:00Z",
    "request_id": "uuid"
  }
}
```

### HTTP Status Codes
- `200 OK`: Success
- `201 Created`: Resource created
- `204 No Content`: Success with no response body
- `400 Bad Request`: Invalid request format
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict (duplicate email)
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

---

## SDKs and Integration

### Official SDKs
- **JavaScript/TypeScript**: `@waitlistbuilder/js-sdk`
- **Python**: `waitlistbuilder-python`
- **Ruby**: `waitlistbuilder-ruby`
- **PHP**: `waitlistbuilder/php-sdk`

### JavaScript SDK Example
```javascript
import WaitlistBuilder from '@waitlistbuilder/js-sdk';

const wb = new WaitlistBuilder({
  projectSlug: 'your-project-slug',
  apiKey: 'your-api-key' // Optional for private operations
});

// Track page view
wb.track('page_view', {
  page_url: window.location.href,
  utm_source: 'twitter'
});

// Submit to waitlist
const result = await wb.join({
  email: '<EMAIL>',
  name: 'John Doe',
  custom_fields: {
    company: 'Acme Corp'
  }
});

// Get project info
const project = await wb.getProject();
```

### Zapier Integration
Connect WaitlistBuilder to 5000+ apps through Zapier:
- **Triggers**: New signup, Email confirmed, Milestone reached
- **Actions**: Add to waitlist, Send custom email, Update entry

### Direct Integrations
- **Mailchimp**: Sync confirmed signups to mailing lists
- **ConvertKit**: Add subscribers to sequences
- **Slack**: Get signup notifications in channels
- **Discord**: Community notifications
- **Google Sheets**: Export data automatically
- **Airtable**: Sync to custom bases

---

## API Security

### Authentication Security
- API keys use secure random generation (256-bit)
- JWT tokens expire after 24 hours
- Rate limiting prevents brute force attacks
- IP-based blocking for suspicious activity

### Data Protection
- All endpoints use HTTPS (TLS 1.3)
- Request/response data encrypted in transit
- No sensitive data in URL parameters
- GDPR-compliant data handling

### Input Validation
- Strict input validation on all endpoints
- SQL injection prevention via parameterized queries
- XSS protection through output encoding
- File upload restrictions and scanning

### CORS Configuration
```
Access-Control-Allow-Origin: https://yourdomain.com
Access-Control-Allow-Methods: GET, POST, PATCH, DELETE
Access-Control-Allow-Headers: Authorization, Content-Type
Access-Control-Max-Age: 86400
```

---

## API Testing

### Postman Collection
Download complete Postman collection with examples:
`https://api.waitlistbuilder.com/postman-collection.json`

### Test Environment
- **Base URL**: `https://api.staging.waitlistbuilder.com`
- **Test API Key**: Available in dashboard settings
- **Sample Project**: `test-project` slug available

### cURL Examples

#### Create Waitlist Signup
```bash
curl -X POST https://api.waitlistbuilder.com/api/v1/projects/your-slug/join \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User",
    "utm_source": "api_test"
  }'
```

#### Get Project Analytics
```bash
curl -X GET "https://api.waitlistbuilder.com/api/v1/projects/123/analytics?start_date=2025-07-01" \
  -H "Authorization: Bearer your_api_key"
```

---

This comprehensive API specification provides everything needed to integrate with WaitlistBuilder, from simple waitlist forms to complex analytics dashboards and webhook systems.