# WaitlistBuilder Development Standards

## Code Quality Standards

### Ruby/Rails Conventions

#### Method Length
- **Maximum 10 lines** per method
- **Single responsibility** - one task per method
- **Extract complex logic** into private methods
- **Use early returns** to reduce nesting

```ruby
# Good
def create_waitlist_entry(params)
  return failure("Invalid project") unless project.active?
  return failure("Email required") if params[:email].blank?
  
  entry = build_entry(params)
  entry.save ? success(entry) : failure(entry.errors)
end

# Bad
def create_waitlist_entry(params)
  if project.active?
    if params[:email].present?
      entry = project.waitlist_entries.build(params)
      entry.email = params[:email].downcase.strip
      entry.queue_position = calculate_position
      entry.confirmation_token = generate_token
      if entry.save
        send_confirmation_email(entry)
        return success(entry)
      else
        return failure(entry.errors)
      end
    else
      return failure("Email required")
    end
  else
    return failure("Invalid project")
  end
end
```

#### Class Organization
```ruby
class WaitlistEntry < ApplicationRecord
  # Constants first
  STATUSES = %w[pending confirmed unsubscribed bounced].freeze
  
  # Associations
  belongs_to :project
  has_many :analytics_events, dependent: :destroy
  
  # Validations
  validates :email, presence: true, uniqueness: { scope: :project_id }
  validates :status, inclusion: { in: STATUSES }
  
  # Scopes
  scope :confirmed, -> { where(status: 'confirmed') }
  scope :recent, -> { order(created_at: :desc) }
  
  # Callbacks
  before_save :normalize_email
  after_create_commit :assign_queue_position
  
  # Public instance methods
  def confirmed?
    status == 'confirmed'
  end
  
  # Private methods
  private
  
  def normalize_email
    self.email = email.downcase.strip
  end
end
```

### Service Object Pattern

#### Base Service Structure
```ruby
class BaseService
  class Result
    attr_reader :data, :errors, :success
    
    def initialize(success:, data: nil, errors: [])
      @success = success
      @data = data
      @errors = Array(errors)
    end
    
    def success?
      @success
    end
    
    def failure?
      !@success
    end
  end
  
  def self.call(*args)
    new(*args).call
  end
  
  private
  
  def success(data = nil)
    Result.new(success: true, data: data)
  end
  
  def failure(errors)
    Result.new(success: false, errors: errors)
  end
end
```

#### Specific Service Implementation
```ruby
class WaitlistEntryService < BaseService
  def initialize(project, params)
    @project = project
    @params = params
  end
  
  def call
    return failure("Project not active") unless @project.active?
    
    ActiveRecord::Base.transaction do
      entry = create_entry
      return failure(entry.errors.full_messages) unless entry.persisted?
      
      send_confirmation_email(entry) if entry.confirmation_required?
      update_project_stats
      broadcast_new_signup(entry)
      
      success(entry)
    end
  rescue StandardError => e
    Rails.logger.error("WaitlistEntryService failed: #{e.message}")
    failure("Unable to process signup")
  end
  
  private
  
  def create_entry
    @project.waitlist_entries.create(@params.merge(
      queue_position: calculate_queue_position,
      confirmation_token: generate_confirmation_token
    ))
  end
end
```

### Controller Patterns

#### API Controllers
```ruby
class Api::V1::WaitlistEntriesController < ApiController
  before_action :authenticate_project!, only: [:index, :show, :destroy]
  before_action :rate_limit_signup, only: [:create]
  
  def create
    result = WaitlistEntryService.call(current_project, entry_params)
    
    if result.success?
      render json: EntrySerializer.new(result.data), status: :created
    else
      render json: { errors: result.errors }, status: :unprocessable_entity
    end
  end
  
  def index
    entries = current_project.waitlist_entries
                            .includes(:analytics_events)
                            .page(params[:page])
                            .per(params[:per_page] || 25)
    
    render json: {
      entries: EntrySerializer.new(entries).serializable_hash,
      pagination: pagination_meta(entries)
    }
  end
  
  private
  
  def entry_params
    params.require(:waitlist_entry).permit(
      :email, :name, :company, :message,
      :utm_source, :utm_medium, :utm_campaign,
      custom_fields: {}
    )
  end
  
  def current_project
    @current_project ||= Project.active.find_by!(slug: params[:project_slug])
  end
  
  def rate_limit_signup
    return unless request.env['rack.attack.throttled']
    
    render json: { 
      error: 'Rate limit exceeded',
      retry_after: request.env['rack.attack.match_data'][:period]
    }, status: :too_many_requests
  end
end
```

#### Dashboard Controllers
```ruby
class Dashboard::ProjectsController < DashboardController
  before_action :authenticate_user!
  before_action :set_project, only: [:show, :edit, :update, :destroy]
  
  def index
    @projects = current_user.projects
                          .includes(:waitlist_entries)
                          .order(updated_at: :desc)
                          .page(params[:page])
  end
  
  def show
    @analytics = ProjectAnalyticsService.call(@project, date_range)
    @recent_entries = @project.waitlist_entries.recent.limit(10)
  end
  
  private
  
  def set_project
    @project = current_user.projects.find(params[:id])
  end
  
  def project_params
    params.require(:project).permit(
      :name, :description, :website_url, :logo,
      :launch_threshold, :requires_confirmation,
      branding_colors: [:primary, :secondary],
      social_links: [:twitter, :linkedin, :facebook]
    )
  end
end
```

## Testing Standards

### Model Testing
```ruby
RSpec.describe WaitlistEntry, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:project) }
    it { is_expected.to have_many(:analytics_events) }
  end
  
  describe 'validations' do
    subject { build(:waitlist_entry) }
    
    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_uniqueness_of(:email).scoped_to(:project_id) }
    it { is_expected.to validate_inclusion_of(:status).in_array(WaitlistEntry::STATUSES) }
  end
  
  describe 'scopes' do
    let!(:confirmed_entry) { create(:waitlist_entry, status: 'confirmed') }
    let!(:pending_entry) { create(:waitlist_entry, status: 'pending') }
    
    describe '.confirmed' do
      it 'returns only confirmed entries' do
        expect(WaitlistEntry.confirmed).to contain_exactly(confirmed_entry)
      end
    end
  end
  
  describe '#confirmed?' do
    it 'returns true when status is confirmed' do
      entry = build(:waitlist_entry, status: 'confirmed')
      expect(entry).to be_confirmed
    end
    
    it 'returns false when status is not confirmed' do
      entry = build(:waitlist_entry, status: 'pending')
      expect(entry).not_to be_confirmed
    end
  end
end
```

### Service Testing
```ruby
RSpec.describe WaitlistEntryService, type: :service do
  let(:project) { create(:project, :active) }
  let(:params) { { email: '<EMAIL>', name: 'John Doe' } }
  
  describe '#call' do
    context 'with valid parameters' do
      it 'creates a waitlist entry' do
        expect { described_class.call(project, params) }
          .to change { project.waitlist_entries.count }.by(1)
      end
      
      it 'returns success result' do
        result = described_class.call(project, params)
        
        expect(result).to be_success
        expect(result.data).to be_a(WaitlistEntry)
        expect(result.data.email).to eq('<EMAIL>')
      end
      
      it 'assigns queue position' do
        create_list(:waitlist_entry, 3, project: project)
        
        result = described_class.call(project, params)
        
        expect(result.data.queue_position).to eq(4)
      end
    end
    
    context 'with invalid parameters' do
      let(:params) { { email: '', name: 'John Doe' } }
      
      it 'returns failure result' do
        result = described_class.call(project, params)
        
        expect(result).to be_failure
        expect(result.errors).to include("Email can't be blank")
      end
      
      it 'does not create a waitlist entry' do
        expect { described_class.call(project, params) }
          .not_to change { project.waitlist_entries.count }
      end
    end
    
    context 'when project is inactive' do
      let(:project) { create(:project, :inactive) }
      
      it 'returns failure result' do
        result = described_class.call(project, params)
        
        expect(result).to be_failure
        expect(result.errors).to include("Project not active")
      end
    end
  end
end
```

### Controller Testing
```ruby
RSpec.describe Api::V1::WaitlistEntriesController, type: :request do
  let(:project) { create(:project, :active) }
  
  describe 'POST /api/v1/projects/:project_slug/entries' do
    let(:url) { "/api/v1/projects/#{project.slug}/entries" }
    let(:params) do
      {
        waitlist_entry: {
          email: '<EMAIL>',
          name: 'John Doe',
          utm_source: 'twitter'
        }
      }
    end
    
    context 'with valid parameters' do
      it 'creates a waitlist entry' do
        expect { post url, params: params }
          .to change { project.waitlist_entries.count }.by(1)
      end
      
      it 'returns created status' do
        post url, params: params
        
        expect(response).to have_http_status(:created)
        expect(json_response['email']).to eq('<EMAIL>')
        expect(json_response['queue_position']).to eq(1)
      end
    end
    
    context 'with invalid parameters' do
      let(:params) { { waitlist_entry: { email: '' } } }
      
      it 'returns unprocessable entity status' do
        post url, params: params
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors']).to be_present
      end
    end
    
    context 'when rate limited', :with_rate_limiting do
      it 'returns too many requests status' do
        # Simulate rate limit exceeded
        allow_any_instance_of(ActionDispatch::Request)
          .to receive(:env).and_return({ 'rack.attack.throttled' => true })
        
        post url, params: params
        
        expect(response).to have_http_status(:too_many_requests)
        expect(json_response['error']).to eq('Rate limit exceeded')
      end
    end
  end
end
```

## Performance Standards

### Database Queries
```ruby
# Good - Single query with includes
def project_dashboard_data
  @project = current_user.projects
                        .includes(:waitlist_entries, :custom_fields)
                        .find(params[:id])
  
  @entries = @project.waitlist_entries
                    .confirmed
                    .includes(:analytics_events)
                    .recent
                    .limit(20)
end

# Bad - N+1 queries
def project_dashboard_data
  @project = current_user.projects.find(params[:id])
  @entries = @project.waitlist_entries.confirmed.recent.limit(20)
  
  # This will cause N+1 queries when accessing analytics_events
  @entries.each { |entry| entry.analytics_events.count }
end
```

### Caching Strategies
```ruby
class ProjectAnalyticsService < BaseService
  def initialize(project, date_range = 7.days.ago..Time.current)
    @project = project
    @date_range = date_range
  end
  
  def call
    Rails.cache.fetch(cache_key, expires_in: 1.hour) do
      calculate_analytics
    end
  end
  
  private
  
  def cache_key
    [
      'project_analytics',
      @project.id,
      @project.updated_at.to_i,
      @date_range.begin.to_date,
      @date_range.end.to_date
    ].join(':')
  end
  
  def calculate_analytics
    {
      total_signups: @project.waitlist_entries.count,
      confirmed_signups: @project.waitlist_entries.confirmed.count,
      conversion_rate: calculate_conversion_rate,
      recent_signups: signups_by_day,
      traffic_sources: traffic_source_breakdown
    }
  end
end
```

### Background Job Optimization
```ruby
class WelcomeEmailJob < ApplicationJob
  queue_as :mailers
  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  
  def perform(waitlist_entry_id)
    entry = WaitlistEntry.find(waitlist_entry_id)
    
    # Batch email processing for efficiency
    WaitlistMailer.welcome_email(entry).deliver_now
    
    # Track delivery for analytics
    entry.analytics_events.create!(
      event_type: 'email_sent',
      metadata: { email_type: 'welcome' }
    )
  rescue ActiveRecord::RecordNotFound
    # Entry was deleted, skip processing
    Rails.logger.info("Skipping welcome email for deleted entry: #{waitlist_entry_id}")
  end
end
```

## Security Standards

### Input Sanitization
```ruby
class InputSanitizer
  def self.sanitize_waitlist_params(params)
    {
      email: sanitize_email(params[:email]),
      name: sanitize_text(params[:name]),
      company: sanitize_text(params[:company]),
      message: sanitize_html(params[:message]),
      custom_fields: sanitize_custom_fields(params[:custom_fields])
    }
  end
  
  private
  
  def self.sanitize_email(email)
    return '' if email.blank?
    email.to_s.downcase.strip.gsub(/[^a-z0-9@._-]/, '')
  end
  
  def self.sanitize_text(text)
    return '' if text.blank?
    ActionController::Base.helpers.strip_tags(text.to_s).strip
  end
  
  def self.sanitize_html(html)
    return '' if html.blank?
    ActionController::Base.helpers.sanitize(html, 
      tags: %w[p br strong em],
      attributes: %w[]
    )
  end
end
```

### API Authentication
```ruby
class ApiController < ApplicationController
  before_action :authenticate_api_request
  
  private
  
  def authenticate_api_request
    return authenticate_with_api_key if api_key_present?
    return authenticate_with_jwt if jwt_token_present?
    
    render json: { error: 'Authentication required' }, status: :unauthorized
  end
  
  def authenticate_with_api_key
    api_key = request.headers['Authorization']&.sub(/^Bearer /, '')
    project = Project.joins(:api_keys).find_by(api_keys: { key_hash: hash_api_key(api_key) })
    
    unless project
      render json: { error: 'Invalid API key' }, status: :unauthorized
      return
    end
    
    @current_project = project
  end
  
  def hash_api_key(key)
    Digest::SHA256.hexdigest("#{key}#{Rails.application.secret_key_base}")
  end
end
```

These standards ensure consistent, secure, and performant code across the WaitlistBuilder application while following Rails best practices and modern development patterns.