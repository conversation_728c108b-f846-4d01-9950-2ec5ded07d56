# WaitlistBuilder Workflow Commands

Quick reference for common development tasks specific to WaitlistBuilder.

## Project Setup Commands

### Initial Setup
```bash
# Complete project setup
rake solid:setup                    # Set up all Solid Trifecta databases
bundle exec rails generate rspec:install  # Initialize RSpec
bundle exec rails generate devise:install # Set up Devise
bundle exec rails generate devise User     # Create User model

# Generate application secret keys
bundle exec rails credentials:edit

# Install and run migrations
bundle exec rails db:create db:migrate db:seed
```

### Environment Setup
```bash
# Development environment
cp .env.example .env              # Copy environment template
bin/dev                          # Start development server with all services

# Test environment  
RAILS_ENV=test rake solid:setup  # Set up test databases
bundle exec rspec                # Run test suite

# Production environment
RAILS_ENV=production rake assets:precompile
RAILS_ENV=production rake db:migrate
```

## Development Workflow

### Feature Development
```bash
# Start new feature (following user memory: always create branch)
git checkout -b feature/waitlist-entry-model
git push -u origin feature/waitlist-entry-model

# Generate models
bundle exec rails generate model WaitlistEntry project:references email:string name:string status:string
bundle exec rails generate model Project user:references name:string slug:string:uniq description:text

# Generate controllers
bundle exec rails generate controller Api::V1::WaitlistEntries
bundle exec rails generate controller Dashboard::Projects

# Generate services
mkdir -p app/services
touch app/services/waitlist_entry_service.rb
touch app/services/project_analytics_service.rb

# Generate jobs
bundle exec rails generate job WelcomeEmail
bundle exec rails generate job UpdateQueuePositions

# Run tests and commit
bundle exec rspec
git add .
git commit -m "feat: add waitlist entry model and API endpoints"
git push origin feature/waitlist-entry-model
```

### Testing Commands
```bash
# Run all tests
bundle exec rspec                              # Full test suite
bundle exec rspec spec/models/                # Model tests only  
bundle exec rspec spec/services/              # Service tests only
bundle exec rspec spec/requests/              # API tests only
bundle exec rspec spec/system/                # E2E tests only

# Run specific test file
bundle exec rspec spec/models/waitlist_entry_spec.rb
bundle exec rspec spec/services/waitlist_entry_service_spec.rb

# Run tests with coverage
COVERAGE=true bundle exec rspec

# Run tests in parallel (for large test suites)
bundle exec parallel_rspec spec/
```

### Database Commands
```bash
# Standard Rails database tasks
bundle exec rails db:create                   # Create databases
bundle exec rails db:migrate                  # Run migrations
bundle exec rails db:rollback                 # Rollback last migration
bundle exec rails db:reset                    # Drop, create, migrate, seed
bundle exec rails db:seed                     # Run seed file

# Solid Trifecta specific tasks
rake solid:db:create                          # Create all Solid databases
rake solid:db:migrate                         # Migrate all Solid databases  
rake solid:db:reset                          # Reset all Solid databases
rake solid:health                            # Health check all components

# Queue management
rake solid:queue:start                       # Start background workers
rake solid:queue:stop                        # Stop background workers
rake solid:queue:status                      # Show queue status
rake solid:queue:clear                       # Clear all jobs

# Cache management
rake solid:cache:clear                       # Clear all cached data
rake solid:cache:stats                       # Show cache statistics
rake solid:cache:warm                        # Warm up cache with data
```

## Code Quality Commands

### Security & Style
```bash
# Security scanning
bundle exec brakeman                         # Security vulnerability scan
bundle exec brakeman --run-all-checks       # Comprehensive security scan
bundle exec bundler-audit check             # Check for vulnerable gems

# Code style and linting
bundle exec rubocop                          # Check Ruby style
bundle exec rubocop -a                       # Auto-fix style issues
bundle exec rubocop --only Layout           # Check only layout issues

# Code quality analysis
bundle exec rails_best_practices             # Rails best practices check
bundle exec reek                            # Code smell detection
```

### Performance & Monitoring
```bash
# Performance profiling
bundle exec rails runner "require 'benchmark'; puts Benchmark.measure { WaitlistEntryService.call(Project.first, {email: '<EMAIL>'}) }"

# Memory profiling
bundle exec derailed_benchmarks:mem          # Memory usage analysis
bundle exec derailed_benchmarks:objects     # Object allocation tracking

# Database query analysis
tail -f log/development.log | grep "ActiveRecord"  # Monitor SQL queries
bundle exec rails db:analyze                # Database performance analysis
```

## API Development Commands

### API Testing
```bash
# Test API endpoints with curl
curl -X POST http://localhost:3000/api/v1/projects/test-project/entries \
  -H "Content-Type: application/json" \
  -d '{"waitlist_entry": {"email": "<EMAIL>", "name": "Test User"}}'

curl -X GET http://localhost:3000/api/v1/projects/test-project \
  -H "Accept: application/json"

# Test with authentication
curl -X GET http://localhost:3000/api/v1/projects \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

### API Documentation
```bash
# Generate API documentation
bundle exec rails api:docs:generate          # Generate API docs
bundle exec rails api:docs:serve            # Serve docs locally

# Validate API responses
bundle exec rails api:validate               # Validate API schema
```

## Deployment Commands

### Local Deployment Testing
```bash
# Build and test Docker container
docker build -t waitlistbuilder .
docker run -p 3000:3000 waitlistbuilder

# Test with Docker Compose
docker-compose up -d                         # Start all services
docker-compose logs -f                       # View logs
docker-compose down                          # Stop all services
```

### Production Deployment
```bash
# Using Kamal (built-in with Rails 8)
kamal setup                                  # Initial server setup
kamal deploy                                 # Deploy application
kamal app logs                              # View application logs
kamal app exec 'rails db:migrate'          # Run migrations in production

# Manual deployment preparation
RAILS_ENV=production bundle exec rails assets:precompile
RAILS_ENV=production bundle exec rails db:migrate
```

## Data Management Commands

### Backup & Restore
```bash
# Database backup
pg_dump waitlistbuilder_production > backup_$(date +%Y%m%d).sql

# Database restore
psql waitlistbuilder_production < backup_20241201.sql

# Export user data (GDPR compliance)
bundle exec rails runner "
  user = User.find_by(email: '<EMAIL>')
  puts JSON.pretty_generate(user.export_data)
"
```

### Data Migration
```bash
# Import data from CSV
bundle exec rails runner "
  CSV.foreach('waitlist_entries.csv', headers: true) do |row|
    WaitlistEntry.create!(row.to_hash)
  end
"

# Export data to CSV
bundle exec rails runner "
  CSV.open('export.csv', 'wb') do |csv|
    csv << WaitlistEntry.column_names
    WaitlistEntry.find_each { |entry| csv << entry.attributes.values }
  end
"
```

## Monitoring Commands

### Real-time Monitoring
```bash
# Monitor application logs
tail -f log/production.log                  # Production logs
tail -f log/development.log                 # Development logs

# Monitor background jobs
watch -n 1 "rake solid:queue:status"       # Queue status updates
watch -n 1 "rake solid:cache:stats"        # Cache statistics

# Monitor system resources
watch -n 1 "ps aux | grep rails"           # Rails processes
watch -n 1 "netstat -tuln | grep 3000"     # Port usage
```

### Health Checks
```bash
# Application health check
curl http://localhost:3000/health           # Basic health endpoint
rake solid:health                          # Comprehensive system health

# Database connectivity
bundle exec rails runner "puts ActiveRecord::Base.connection.active?"

# External service health
bundle exec rails runner "
  puts 'Email service: ' + (ActionMailer::Base.delivery_method == :smtp ? 'OK' : 'NOT CONFIGURED')
  puts 'Cache: ' + (Rails.cache.write('test', 'value') ? 'OK' : 'ERROR')
"
```

## Troubleshooting Commands

### Debug Common Issues
```bash
# Fix permission issues
chmod +x bin/dev bin/rails bin/rake

# Clear various caches
bundle exec rails dev:cache                 # Toggle development caching
bundle exec rails tmp:clear                 # Clear temporary files
bundle exec rails log:clear                 # Clear log files

# Reset development environment
bundle exec rails db:drop db:create db:migrate db:seed
rake solid:db:reset

# Fix asset issues
bundle exec rails assets:clobber            # Remove compiled assets
bundle exec rails assets:precompile         # Recompile assets
```

### Performance Debugging
```bash
# Profile specific endpoints
bundle exec rails runner "
  require 'benchmark'
  puts Benchmark.measure { 
    app.get('/api/v1/projects/test-project') 
  }
"

# Check for N+1 queries
bundle exec rails console
> ActiveRecord::Base.logger = Logger.new(STDOUT)
> # Run your code here to see SQL queries
```

## Git Workflow Integration

### Branch Management
```bash
# Feature branch workflow (as per user memory)
git checkout -b feature/new-feature          # Create feature branch
git add .                                   # Stage changes
git commit -m "feat: implement new feature" # Commit with conventional format
git push -u origin feature/new-feature      # Push and set upstream

# Before merging
bundle exec rspec                           # Run tests
bundle exec brakeman                        # Security scan
bundle exec rubocop                         # Code style check

# Create pull request (using GitHub CLI)
gh pr create --title "Add new feature" --body "Description of changes"
```

### Release Management
```bash
# Prepare release
git checkout main
git pull origin main
git checkout -b release/v1.2.0

# Update version and changelog
# ... make version updates ...

git commit -m "chore: prepare release v1.2.0"
git push -u origin release/v1.2.0

# After approval and merge
git tag v1.2.0
git push origin v1.2.0
```

These commands provide a comprehensive workflow for developing, testing, and deploying the WaitlistBuilder application efficiently.