# WaitlistBuilder Project Configuration

Project-specific Claude Code configuration for WaitlistBuilder SaaS application.

## Project Context

**Application**: WaitlistBuilder - Pre-launch waitlist landing page SaaS  
**Tech Stack**: Rails 8, PostgreSQL, Solid Trifecta, TailwindCSS, Hotwire  
**Architecture**: Multi-tenant SaaS with public landing pages and private dashboards

## Development Workflow

### Git Workflow
- **ALWAYS** create a new branch for every task: `git checkout -b feature/task-name`
- Test thoroughly before committing
- Commit with descriptive messages following conventional commits
- Push branch and create PR for review

### Branch Naming Convention
```
feature/user-authentication
feature/waitlist-entry-model
feature/project-dashboard
feature/email-sequences
bugfix/queue-position-calculation
hotfix/security-vulnerability
refactor/service-objects
docs/api-specification
```

### Testing Strategy
- **TDD Approach**: Write tests before implementation
- **Coverage Target**: 90%+ for critical paths
- **Test Types**: Unit (70%), Integration (20%), E2E (10%)
- **Run tests**: `bundle exec rspec`
- **Security scan**: `bundle exec brakeman`

## Project-Specific Commands

### Database Management
```bash
# Setup all Solid Trifecta databases
rake solid:setup

# Health check all components
rake solid:health

# Reset development data
rake solid:db:reset
```

### Development Server
```bash
# Start with Solid Trifecta
bin/dev

# Background jobs only
rake solid:queue:start
```

### Security & Quality
```bash
# Security scan
bundle exec brakeman

# Code style
bundle exec rubocop

# Dependency audit
bundle audit
```

## Code Organization

### Service Objects Pattern
All business logic in service objects under `app/services/`:
```ruby
class WaitlistEntryService < BaseService
  def initialize(project, params)
    @project = project
    @params = params
  end
  
  def call
    # Implementation
  end
end
```

### Controller Pattern
Keep controllers thin:
```ruby
class Api::V1::WaitlistEntriesController < ApiController
  def create
    result = WaitlistEntryService.new(current_project, entry_params).call
    
    if result.success?
      render json: result.data, status: :created
    else
      render json: { errors: result.errors }, status: :unprocessable_entity
    end
  end
end
```

### Model Concerns
Use concerns for shared behavior:
```ruby
module Trackable
  extend ActiveSupport::Concern
  
  included do
    after_create_commit :track_creation
  end
end
```

## Security Guidelines

### Authentication
- Use Devise with enhanced security configuration
- JWT tokens for API access
- Rate limiting on all public endpoints

### Data Protection
- Encrypt PII data using Active Record Encryption
- Sanitize all user inputs
- Use parameterized queries only

### API Security
- CORS configured for project domains only
- API key authentication for server-to-server
- Request size limits and timeout protection

## Performance Standards

### Response Time Targets
- Dashboard pages: < 200ms
- API endpoints: < 100ms
- Public waitlist pages: < 150ms
- Background jobs: Complete within 30s

### Caching Strategy
- Page caching for public pages (6 hours)
- Fragment caching for dynamic content (1 hour)
- API response caching (5 minutes)
- Analytics data caching (30 minutes)

### Database Optimization
- Index all foreign keys and query columns
- Use database-level constraints
- Implement soft deletes for audit trails
- Regular VACUUM and ANALYZE in production

## Monitoring & Observability

### Key Metrics
- Signup conversion rate by traffic source
- Email delivery and open rates
- Queue position update performance
- Background job processing times

### Alerting Thresholds
- Response time > 500ms (warning)
- Error rate > 1% (critical)
- Queue processing delays > 5 minutes (warning)
- Database connection pool > 80% (warning)

### Logging Standards
```ruby
Rails.logger.info({
  event: 'waitlist_signup',
  project_id: project.id,
  user_email: entry.email,
  source: params[:utm_source],
  duration_ms: (Time.current - start_time) * 1000
}.to_json)
```

## File Conventions

### Naming Patterns
- Models: `WaitlistEntry`, `EmailTemplate`
- Controllers: `WaitlistEntriesController`, `Api::V1::ProjectsController`
- Services: `WaitlistEntryService`, `EmailDeliveryService`
- Jobs: `WelcomeEmailJob`, `UpdateQueuePositionsJob`
- Specs: `waitlist_entry_spec.rb`, `email_delivery_service_spec.rb`

### Directory Structure
```
app/
├── controllers/
│   ├── api/v1/               # API controllers
│   ├── dashboard/            # Admin dashboard
│   └── public/               # Public waitlist pages
├── models/
│   └── concerns/             # Shared model behavior
├── services/                 # Business logic
├── jobs/                     # Background jobs
├── mailers/                  # Email templates
└── views/
    ├── layouts/
    ├── dashboard/            # Admin views
    └── public/               # Public page views
```

## Environment Configuration

### Development
- Hot reloading enabled
- Detailed error pages
- SQL query logging
- Letter opener for emails

### Staging
- Production-like data
- SSL enabled  
- Performance monitoring
- Error tracking

### Production
- SSL enforced
- Asset compilation
- Background job workers
- Comprehensive monitoring

## Quality Gates

Before any merge:
1. ✅ All tests passing (`bundle exec rspec`)
2. ✅ Security scan clean (`bundle exec brakeman`)
3. ✅ Code style compliant (`bundle exec rubocop`)
4. ✅ No database migrations in conflict
5. ✅ Performance benchmarks met
6. ✅ Documentation updated

## Dependencies

### Core Gems
- `rails` - Framework
- `pg` - Database
- `devise` - Authentication
- `acts_as_tenant` - Multi-tenancy
- `solid_*` - Background processing, caching, WebSockets

### Development & Testing
- `rspec-rails` - Testing framework
- `factory_bot_rails` - Test data
- `capybara` - Integration testing
- `brakeman` - Security scanning

This configuration ensures consistent development practices specifically tailored for the WaitlistBuilder project while following Rails and SuperClaude best practices.