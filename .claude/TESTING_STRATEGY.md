# WaitlistBuilder Testing Strategy

## Testing Philosophy

### Test-Driven Development (TDD)
- **Red-Green-Refactor** cycle for all new features
- **Write tests before implementation** to clarify requirements
- **Comprehensive coverage** for critical business logic
- **Fast feedback loops** with optimized test suites

### Testing Pyramid
```
         /\
        /  \
       / E2E \     10% - End-to-end tests
      /______\
     /        \
    /Integration\ 20% - Integration tests  
   /____________\
  /              \
 /  Unit Tests    \ 70% - Unit tests
/__________________\
```

## Test Categories

### Unit Tests (70% of test suite)
**Purpose**: Test individual methods and classes in isolation  
**Tools**: RSpec, FactoryBot, Shoulda Matchers  
**Speed**: < 0.1s per test  
**Coverage**: All models, services, helpers, and utilities

```ruby
# Example: Model unit test
RSpec.describe WaitlistEntry, type: :model do
  describe '#calculate_queue_position' do
    let(:project) { create(:project) }
    
    it 'returns 1 for first entry' do
      entry = build(:waitlist_entry, project: project)
      expect(entry.calculate_queue_position).to eq(1)
    end
    
    it 'returns correct position based on confirmed entries' do
      create_list(:waitlist_entry, 3, project: project, status: 'confirmed')
      create(:waitlist_entry, project: project, status: 'pending')
      
      entry = build(:waitlist_entry, project: project)
      expect(entry.calculate_queue_position).to eq(4)
    end
  end
end
```

### Integration Tests (20% of test suite)
**Purpose**: Test component interactions and workflows  
**Tools**: RSpec, Capybara, VCR, WebMock  
**Speed**: < 2s per test  
**Coverage**: Controllers, APIs, email delivery, job processing

```ruby
# Example: API integration test
RSpec.describe 'Waitlist Entry API', type: :request do
  let(:project) { create(:project, :active) }
  
  describe 'POST /api/v1/projects/:slug/entries' do
    it 'creates entry and sends confirmation email' do
      expect {
        post "/api/v1/projects/#{project.slug}/entries", params: {
          waitlist_entry: {
            email: '<EMAIL>',
            name: 'John Doe'
          }
        }
      }.to change { ActionMailer::Base.deliveries.count }.by(1)
      
      expect(response).to have_http_status(:created)
      expect(json_response['email']).to eq('<EMAIL>')
      
      entry = WaitlistEntry.last
      expect(entry.confirmation_token).to be_present
    end
  end
end
```

### End-to-End Tests (10% of test suite)
**Purpose**: Test complete user workflows from browser perspective  
**Tools**: Capybara, Selenium WebDriver, Chrome headless  
**Speed**: < 30s per test  
**Coverage**: Critical user journeys, payment flows, signup processes

```ruby
# Example: E2E user journey test
RSpec.describe 'Waitlist signup flow', type: :system do
  let(:project) { create(:project, :active, slug: 'awesome-app') }
  
  it 'allows user to join waitlist and receive confirmation' do
    visit "/#{project.slug}"
    
    expect(page).to have_content(project.name)
    expect(page).to have_content(project.description)
    
    fill_in 'Email', with: '<EMAIL>'
    fill_in 'Name', with: 'John Doe'
    click_button 'Join Waitlist'
    
    expect(page).to have_content('Successfully joined the waitlist!')
    expect(page).to have_content('You are #1 in line')
    
    # Verify email was sent
    expect(ActionMailer::Base.deliveries.last.to).to include('<EMAIL>')
  end
  
  it 'shows live signup counter updates' do
    visit "/#{project.slug}"
    initial_count = find('[data-testid="signup-count"]').text.to_i
    
    # Simulate another user signing up in background
    create(:waitlist_entry, project: project, status: 'confirmed')
    
    # Counter should update via WebSocket
    expect(page).to have_css('[data-testid="signup-count"]', 
                            text: (initial_count + 1).to_s)
  end
end
```

## Testing Configuration

### RSpec Configuration
```ruby
# spec/rails_helper.rb
require 'spec_helper'
require File.expand_path('../config/environment', __dir__)
require 'rspec/rails'
require 'capybara/rails'
require 'capybara/rspec'
require 'selenium-webdriver'

RSpec.configure do |config|
  # Database cleaning strategy
  config.use_transactional_fixtures = false
  
  config.before(:suite) do
    DatabaseCleaner.clean_with(:truncation)
  end
  
  config.before(:each) do
    DatabaseCleaner.strategy = :transaction
  end
  
  config.before(:each, type: :system) do
    DatabaseCleaner.strategy = :truncation
  end
  
  config.before(:each) do
    DatabaseCleaner.start
  end
  
  config.after(:each) do
    DatabaseCleaner.clean
  end
  
  # System test configuration
  config.before(:each, type: :system) do
    driven_by :selenium, using: :headless_chrome, screen_size: [1400, 1400]
  end
  
  # Include custom helpers
  config.include RequestHelpers, type: :request
  config.include SystemHelpers, type: :system
  config.include EmailHelpers
  config.include AuthenticationHelpers
end
```

### Test Factories
```ruby
# spec/factories/projects.rb
FactoryBot.define do
  factory :project do
    user
    name { Faker::Company.name }
    slug { name.parameterize }
    description { Faker::Company.catch_phrase }
    website_url { Faker::Internet.url }
    status { 'active' }
    requires_confirmation { true }
    launch_threshold { 1000 }
    
    trait :active do
      status { 'active' }
    end
    
    trait :inactive do
      status { 'draft' }
    end
    
    trait :with_entries do
      after(:create) do |project|
        create_list(:waitlist_entry, 5, project: project)
      end
    end
    
    trait :with_custom_fields do
      after(:create) do |project|
        create(:custom_field, :text, project: project, name: 'company')
        create(:custom_field, :select, project: project, name: 'role')
      end
    end
  end
end

# spec/factories/waitlist_entries.rb
FactoryBot.define do
  factory :waitlist_entry do
    project
    email { Faker::Internet.email }
    name { Faker::Name.name }
    status { 'pending' }
    queue_position { 1 }
    
    trait :confirmed do
      status { 'confirmed' }
      confirmed_at { 1.hour.ago }
    end
    
    trait :with_utm_data do
      utm_source { 'twitter' }
      utm_medium { 'social' }
      utm_campaign { 'launch' }
    end
    
    trait :with_referral do
      referral_code { SecureRandom.urlsafe_base64(8) }
      referred_by { create(:waitlist_entry, project: project) }
    end
  end
end
```

### Test Helpers
```ruby
# spec/support/request_helpers.rb
module RequestHelpers
  def json_response
    JSON.parse(response.body)
  end
  
  def auth_headers(user)
    token = JWT.encode(
      { user_id: user.id, exp: 24.hours.from_now.to_i },
      Rails.application.credentials.secret_key_base,
      'HS256'
    )
    { 'Authorization' => "Bearer #{token}" }
  end
end

# spec/support/system_helpers.rb
module SystemHelpers
  def sign_in_user(user)
    visit new_user_session_path
    fill_in 'Email', with: user.email
    fill_in 'Password', with: user.password
    click_button 'Sign in'
  end
  
  def expect_flash_message(message, type: :notice)
    expect(page).to have_css("[data-flash-type='#{type}']", text: message)
  end
  
  def wait_for_ajax
    Timeout.timeout(Capybara.default_max_wait_time) do
      loop until page.evaluate_script('jQuery.active').zero?
    end
  end
end

# spec/support/email_helpers.rb
module EmailHelpers
  def last_email
    ActionMailer::Base.deliveries.last
  end
  
  def emails_sent_to(email_address)
    ActionMailer::Base.deliveries.select { |mail| mail.to.include?(email_address) }
  end
  
  def clear_emails
    ActionMailer::Base.deliveries.clear
  end
end
```

## Performance Testing

### Load Testing with Artillery
```yaml
# artillery.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 300
      arrivalRate: 50
      name: "Load test"
    - duration: 60
      arrivalRate: 100
      name: "Spike test"

scenarios:
  - name: "Waitlist signup flow"
    weight: 70
    flow:
      - get:
          url: "/awesome-app"
      - think: 5
      - post:
          url: "/api/v1/projects/awesome-app/entries"
          json:
            waitlist_entry:
              email: "user{{ $randomString() }}@example.com"
              name: "{{ $randomString() }}"
              
  - name: "Dashboard access"
    weight: 30
    flow:
      - post:
          url: "/users/sign_in"
          form:
            user[email]: "<EMAIL>"
            user[password]: "password"
      - get:
          url: "/dashboard/projects"
```

### Benchmark Testing
```ruby
# spec/benchmarks/waitlist_entry_creation_spec.rb
require 'benchmark'

RSpec.describe 'WaitlistEntry creation performance' do
  let(:project) { create(:project) }
  
  it 'creates entries within performance threshold' do
    time = Benchmark.realtime do
      100.times do |i|
        WaitlistEntryService.call(project, {
          email: "user#{i}@example.com",
          name: "User #{i}"
        })
      end
    end
    
    # Should create 100 entries in under 2 seconds
    expect(time).to be < 2.0
    
    # Average time per entry should be under 20ms
    average_time = (time / 100) * 1000
    expect(average_time).to be < 20
  end
end
```

## Test Data Management

### Database Seeding for Tests
```ruby
# spec/support/test_data.rb
class TestData
  def self.create_sample_project_with_data
    project = create(:project, :active, name: 'Sample App')
    
    # Create entries with realistic distribution
    50.times do |i|
      create(:waitlist_entry, 
        project: project,
        status: i < 45 ? 'confirmed' : 'pending',
        created_at: rand(30.days).seconds.ago,
        utm_source: %w[twitter facebook google direct].sample
      )
    end
    
    # Create some analytics events
    project.waitlist_entries.each do |entry|
      rand(1..5).times do
        create(:analytics_event,
          project: project,
          event_type: %w[page_view form_focus form_submit].sample,
          created_at: rand(7.days).seconds.ago
        )
      end
    end
    
    project
  end
end
```

### Test Environment Configuration
```ruby
# config/environments/test.rb
Rails.application.configure do
  # Disable cache and eager loading
  config.cache_classes = false
  config.eager_load = false
  
  # Show exceptions instead of rescue responses
  config.consider_all_requests_local = true
  
  # Disable caching for tests
  config.action_controller.perform_caching = false
  config.cache_store = :null_store
  
  # Don't send emails in test
  config.action_mailer.delivery_method = :test
  config.action_mailer.perform_deliveries = false
  
  # Use test adapter for Active Job
  config.active_job.queue_adapter = :test
  
  # Fast password hashing for tests
  config.active_model.belongs_to_required_by_default = true
  
  # Disable SQL schema dumping
  config.active_record.dump_schema_after_migration = false
  
  # Raise exceptions for deprecation warnings
  config.active_support.deprecation = :raise
end
```

## CI/CD Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: 3.2
        bundler-cache: true
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        bundle install
        npm install
    
    - name: Set up database
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/waitlistbuilder_test
      run: |
        bundle exec rails db:create
        bundle exec rails db:migrate
    
    - name: Run tests
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/waitlistbuilder_test
        RAILS_ENV: test
      run: |
        bundle exec rspec --format RspecJunitFormatter --out test_results/rspec.xml
    
    - name: Run security scan
      run: bundle exec brakeman --no-pager
    
    - name: Run style check
      run: bundle exec rubocop
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: test_results/
```

## Coverage Goals

### Coverage Targets
- **Unit Tests**: 95% coverage for models and services
- **Integration Tests**: 90% coverage for controllers and APIs
- **System Tests**: 80% coverage for critical user paths
- **Overall**: 90% total coverage with no critical gaps

### Coverage Tracking
```ruby
# spec/spec_helper.rb
require 'simplecov'

SimpleCov.start 'rails' do
  add_filter '/spec/'
  add_filter '/config/'
  add_filter '/vendor/'
  
  add_group 'Models', 'app/models'
  add_group 'Controllers', 'app/controllers'
  add_group 'Services', 'app/services'
  add_group 'Jobs', 'app/jobs'
  add_group 'Mailers', 'app/mailers'
  
  minimum_coverage 90
  refuse_coverage_drop
end
```

This comprehensive testing strategy ensures high-quality, maintainable code while providing fast feedback during development and reliable deployment confidence.