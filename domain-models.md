# WaitlistBuilder: Domain Models & Database Schema

## Overview

This document defines the complete domain model for WaitlistBuilder, including database schemas, relationships, validations, and business logic. The design follows Domain-Driven Design (DDD) principles with clear separation of concerns.

---

## Core Domain Entities

### 1. User Model
**Purpose**: Represents application users with subscription-based access control

#### Database Schema
```ruby
# Migration: devise_create_users.rb
class DeviseCreateUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :users do |t|
      # Devise fields
      t.string :email,              null: false, default: ""
      t.string :encrypted_password, null: false, default: ""
      t.string :reset_password_token
      t.datetime :reset_password_sent_at
      t.datetime :remember_created_at
      t.string :confirmation_token
      t.datetime :confirmed_at
      t.datetime :confirmation_sent_at
      
      # Application fields
      t.string :name,               null: false
      t.string :company
      t.string :avatar_url
      t.integer :subscription_tier, default: 0
      t.datetime :subscription_expires_at
      t.json :subscription_metadata, default: {}
      t.boolean :email_notifications, default: true
      t.string :timezone, default: 'UTC'
      t.datetime :last_active_at
      
      t.timestamps null: false
    end
    
    add_index :users, :email,                unique: true
    add_index :users, :reset_password_token, unique: true
    add_index :users, :confirmation_token,   unique: true
    add_index :users, :subscription_tier
    add_index :users, :last_active_at
  end
end
```

#### Model Definition
```ruby
# app/models/user.rb
class User < ApplicationRecord
  devise :database_authenticatable, :registerable, :recoverable, 
         :rememberable, :validatable, :confirmable, :trackable

  # Enums
  enum subscription_tier: { 
    free: 0, 
    starter: 1, 
    growth: 2, 
    pro: 3, 
    enterprise: 4 
  }
  
  # Associations
  has_many :projects, dependent: :destroy
  has_many :waitlist_entries, through: :projects
  has_many :team_memberships, dependent: :destroy
  has_many :owned_teams, class_name: 'Team', foreign_key: 'owner_id'
  
  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 50 }
  validates :company, length: { maximum: 100 }
  validates :timezone, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name) }
  
  # Callbacks
  before_save :update_last_active_at, if: :will_save_change_to_updated_at?
  
  # Scopes
  scope :active, -> { where('last_active_at > ?', 30.days.ago) }
  scope :with_subscription, -> { where.not(subscription_tier: :free) }
  
  # Business Logic
  def can_create_project?
    case subscription_tier
    when 'free' then projects.count < 1
    when 'starter' then projects.count < 3
    when 'growth' then projects.count < 10
    when 'pro' then projects.count < 50
    else true
    end
  end
  
  def project_limit
    case subscription_tier
    when 'free' then 1
    when 'starter' then 3
    when 'growth' then 10
    when 'pro' then 50
    else Float::INFINITY
    end
  end
  
  def subscriber_limit
    case subscription_tier
    when 'free' then 100
    when 'starter' then 1_000
    when 'growth' then 5_000
    when 'pro' then 25_000
    else Float::INFINITY
    end
  end
  
  def total_signups
    waitlist_entries.confirmed.count
  end
  
  def subscription_active?
    return true if enterprise?
    subscription_expires_at.nil? || subscription_expires_at > Time.current
  end
  
  def display_name
    name.presence || email.split('@').first.humanize
  end
  
  private
  
  def update_last_active_at
    self.last_active_at = Time.current
  end
end
```

#### Business Rules
- Free users: 1 project, 100 subscribers max
- Starter users: 3 projects, 1,000 subscribers max  
- Growth users: 10 projects, 5,000 subscribers max
- Pro users: 50 projects, 25,000 subscribers max
- Enterprise users: Unlimited everything

---

### 2. Project Model
**Purpose**: Represents individual waitlist campaigns with configuration and branding

#### Database Schema
```ruby
# Migration: create_projects.rb
class CreateProjects < ActiveRecord::Migration[7.0]
  def change
    create_table :projects do |t|
      t.references :user, null: false, foreign_key: true
      
      # Basic Information
      t.string :name, null: false
      t.string :slug, null: false
      t.text :description, null: false
      t.string :website_url
      t.string :logo_url
      t.text :thank_you_message
      
      # Status and Configuration
      t.integer :status, default: 0
      t.integer :launch_threshold, default: 100
      t.datetime :launch_date
      t.boolean :requires_confirmation, default: true
      t.boolean :collect_names, default: true
      t.boolean :collect_companies, default: false
      t.boolean :show_position, default: true
      t.boolean :show_progress, default: true
      
      # Customization
      t.json :branding_colors, default: {}
      t.json :custom_css, default: {}
      t.json :social_links, default: {}
      
      # SEO and Social
      t.string :meta_title
      t.text :meta_description
      t.string :og_image_url
      
      # Analytics Cache (for performance)
      t.integer :signup_count, default: 0
      t.integer :page_views, default: 0
      t.decimal :conversion_rate, precision: 5, scale: 2, default: 0.0
      t.datetime :stats_updated_at
      
      t.timestamps
    end
    
    add_index :projects, :slug, unique: true
    add_index :projects, :user_id
    add_index :projects, :status
    add_index :projects, :launch_date
    add_index :projects, [:user_id, :status]
  end
end
```

#### Model Definition
```ruby
# app/models/project.rb
class Project < ApplicationRecord
  belongs_to :user
  has_many :waitlist_entries, dependent: :destroy
  has_many :custom_fields, dependent: :destroy
  has_many :email_sequences, dependent: :destroy
  has_many :analytics_events, dependent: :destroy
  has_many :referrals, dependent: :destroy
  
  # Enums
  enum status: { 
    draft: 0, 
    active: 1, 
    paused: 2,
    closed: 3, 
    archived: 4 
  }
  
  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :slug, presence: true, uniqueness: true, 
            format: { with: /\A[a-z0-9\-]+\z/, message: "only lowercase letters, numbers, and dashes" }
  validates :description, presence: true, length: { minimum: 10, maximum: 500 }
  validates :website_url, url: true, allow_blank: true
  validates :logo_url, url: true, allow_blank: true
  validates :launch_threshold, presence: true, 
            numericality: { greater_than: 0, less_than_or_equal_to: 100_000 }
  
  # Callbacks
  before_validation :generate_slug, if: -> { slug.blank? && name.present? }
  before_validation :set_meta_fields, if: :will_save_change_to_name?
  after_update :update_cached_stats, if: :saved_change_to_status?
  
  # Scopes
  scope :publicly_visible, -> { where(status: [:active, :paused]) }
  scope :active_projects, -> { where(status: :active) }
  scope :by_user, ->(user) { where(user: user) }
  scope :recent, -> { order(created_at: :desc) }
  
  # Business Logic
  def can_be_activated?
    draft? && description.present? && name.present?
  end
  
  def launch_ready?
    confirmed_signups >= launch_threshold
  end
  
  def confirmed_signups
    waitlist_entries.confirmed.count
  end
  
  def pending_signups
    waitlist_entries.pending.count
  end
  
  def total_signups
    waitlist_entries.count
  end
  
  def current_conversion_rate
    return 0.0 if page_views.zero?
    (confirmed_signups.to_f / page_views * 100).round(2)
  end
  
  def progress_percentage
    return 0 if launch_threshold.zero?
    [(confirmed_signups.to_f / launch_threshold * 100).round(1), 100.0].min
  end
  
  def public_url
    Rails.application.routes.url_helpers.public_waitlist_url(slug)
  end
  
  def to_param
    slug
  end
  
  # Stats Management
  def update_cached_stats!
    self.signup_count = confirmed_signups
    self.conversion_rate = current_conversion_rate
    self.stats_updated_at = Time.current
    save!
  end
  
  private
  
  def generate_slug
    base_slug = name.parameterize
    counter = 1
    potential_slug = base_slug
    
    while Project.exists?(slug: potential_slug)
      potential_slug = "#{base_slug}-#{counter}"
      counter += 1
    end
    
    self.slug = potential_slug
  end
  
  def set_meta_fields
    self.meta_title ||= "#{name} - Join the Waitlist"
    self.meta_description ||= description.truncate(160)
  end
  
  def update_cached_stats
    UpdateProjectStatsJob.perform_later(self)
  end
end
```

#### Business Rules
- Slug must be unique across all projects
- Only active/paused projects are publicly accessible
- Launch threshold cannot exceed user's subscriber limit
- Meta fields auto-generate from project name/description

---

### 3. WaitlistEntry Model
**Purpose**: Represents individual subscribers on project waitlists

#### Database Schema
```ruby
# Migration: create_waitlist_entries.rb
class CreateWaitlistEntries < ActiveRecord::Migration[7.0]
  def change
    create_table :waitlist_entries do |t|
      t.references :project, null: false, foreign_key: true
      
      # Core Data
      t.string :email, null: false
      t.string :name
      t.string :company
      t.text :message
      
      # Status Management
      t.integer :status, default: 0
      t.string :confirmation_token
      t.datetime :confirmed_at
      t.datetime :unsubscribed_at
      t.text :unsubscribe_reason
      
      # Tracking Data
      t.string :ip_address
      t.string :user_agent
      t.string :referrer
      t.string :utm_source
      t.string :utm_medium
      t.string :utm_campaign
      t.string :utm_content
      t.string :utm_term
      
      # Referral System
      t.references :referrer_entry, null: true, foreign_key: { to_table: :waitlist_entries }
      t.string :referral_code
      t.integer :referral_count, default: 0
      
      # Queue Management
      t.integer :queue_position
      t.datetime :position_updated_at
      
      t.timestamps
    end
    
    add_index :waitlist_entries, [:project_id, :email], unique: true
    add_index :waitlist_entries, :confirmation_token, unique: true
    add_index :waitlist_entries, :status
    add_index :waitlist_entries, :created_at
    add_index :waitlist_entries, [:project_id, :status, :created_at]
    add_index :waitlist_entries, :referral_code, unique: true
    add_index :waitlist_entries, :queue_position
  end
end
```

#### Model Definition
```ruby
# app/models/waitlist_entry.rb
class WaitlistEntry < ApplicationRecord
  belongs_to :project
  belongs_to :referrer_entry, class_name: 'WaitlistEntry', optional: true
  has_many :referred_entries, class_name: 'WaitlistEntry', foreign_key: 'referrer_entry_id'
  has_many :custom_field_values, dependent: :destroy
  has_many :email_deliveries, dependent: :destroy
  
  # Enums
  enum status: { 
    pending: 0, 
    confirmed: 1, 
    unsubscribed: 2, 
    bounced: 3,
    invalid: 4
  }
  
  # Validations
  validates :email, presence: true, 
            format: { with: URI::MailTo::EMAIL_REGEXP },
            uniqueness: { scope: :project_id, case_sensitive: false }
  validates :name, length: { maximum: 100 }
  validates :company, length: { maximum: 100 }
  validates :message, length: { maximum: 1000 }
  validates :referral_code, uniqueness: true, allow_nil: true
  
  # Callbacks
  before_create :generate_confirmation_token
  before_create :generate_referral_code
  after_create :send_confirmation_email, if: -> { project.requires_confirmation? }
  after_create :trigger_welcome_sequence, unless: -> { project.requires_confirmation? }
  after_update :trigger_welcome_sequence, if: :saved_change_to_status?
  after_create :update_queue_positions
  after_destroy :update_queue_positions
  
  # Scopes
  scope :confirmed, -> { where(status: :confirmed) }
  scope :pending, -> { where(status: :pending) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_queue_position, -> { order(:queue_position) }
  
  # Queue Management
  def calculate_queue_position
    project.waitlist_entries
           .confirmed
           .where('created_at <= ?', created_at || Time.current)
           .count
  end
  
  def update_queue_position!
    new_position = calculate_queue_position
    update!(queue_position: new_position, position_updated_at: Time.current)
    new_position
  end
  
  # Status Management
  def confirm!
    return false if confirmed?
    
    transaction do
      update!(
        status: :confirmed, 
        confirmed_at: Time.current,
        queue_position: calculate_queue_position
      )
      
      # Update referrer stats
      referrer_entry&.increment!(:referral_count)
      
      # Trigger welcome sequence
      trigger_welcome_sequence
    end
    
    true
  end
  
  def unsubscribe!(reason = nil)
    update!(
      status: :unsubscribed,
      unsubscribed_at: Time.current,
      unsubscribe_reason: reason
    )
  end
  
  # Referral System
  def referral_url
    return nil unless referral_code
    Rails.application.routes.url_helpers.public_waitlist_url(
      project.slug, 
      ref: referral_code
    )
  end
  
  def referral_stats
    {
      total_referrals: referral_count,
      successful_referrals: referred_entries.confirmed.count,
      pending_referrals: referred_entries.pending.count
    }
  end
  
  # Custom Fields
  def custom_field_value(field_name)
    custom_field_values.joins(:custom_field)
                      .find_by(custom_fields: { name: field_name })
                      &.value
  end
  
  def set_custom_field_value(field_name, value)
    field = project.custom_fields.find_by(name: field_name)
    return false unless field
    
    custom_field_values.find_or_initialize_by(custom_field: field).tap do |cfv|
      cfv.value = value
      cfv.save!
    end
  end
  
  # Display Methods
  def display_name
    name.presence || email.split('@').first.humanize
  end
  
  def display_position
    return "Confirmed!" if confirmed?
    return "Pending confirmation" if pending?
    queue_position || calculate_queue_position
  end
  
  private
  
  def generate_confirmation_token
    self.confirmation_token = SecureRandom.urlsafe_base64(32)
  end
  
  def generate_referral_code
    loop do
      code = SecureRandom.alphanumeric(8).upcase
      unless WaitlistEntry.exists?(referral_code: code)
        self.referral_code = code
        break
      end
    end
  end
  
  def send_confirmation_email
    WaitlistMailer.confirmation_email(self).deliver_later
  end
  
  def trigger_welcome_sequence
    return unless confirmed?
    EmailSequenceJob.perform_later(self, 'confirmed')
  end
  
  def update_queue_positions
    UpdateQueuePositionsJob.perform_later(project)
  end
end
```

#### Business Rules
- Email must be unique per project (case-insensitive)
- Referral codes are auto-generated and unique across all entries
- Queue position calculated based on confirmed entries only
- Custom field values are validated against field definitions

---

### 4. CustomField Model
**Purpose**: Flexible form field system for gathering specific insights

#### Database Schema
```ruby
# Migration: create_custom_fields.rb
class CreateCustomFields < ActiveRecord::Migration[7.0]
  def change
    create_table :custom_fields do |t|
      t.references :project, null: false, foreign_key: true
      
      # Field Definition
      t.string :name, null: false
      t.string :label, null: false
      t.integer :field_type, null: false
      t.boolean :required, default: false
      t.json :options, default: []
      t.text :help_text
      t.text :validation_rules
      
      # Display Configuration
      t.integer :display_order, default: 0
      t.boolean :active, default: true
      t.boolean :show_in_summary, default: false
      
      # Conditional Logic
      t.json :show_conditions, default: {}
      
      t.timestamps
    end
    
    add_index :custom_fields, :project_id
    add_index :custom_fields, [:project_id, :name], unique: true
    add_index :custom_fields, :display_order
    add_index :custom_fields, :active
  end
end

# Migration: create_custom_field_values.rb
class CreateCustomFieldValues < ActiveRecord::Migration[7.0]
  def change
    create_table :custom_field_values do |t|
      t.references :waitlist_entry, null: false, foreign_key: true
      t.references :custom_field, null: false, foreign_key: true
      t.text :value
      t.timestamps
    end
    
    add_index :custom_field_values, [:waitlist_entry_id, :custom_field_id], 
              unique: true, name: 'index_custom_field_values_on_entry_and_field'
  end
end
```

#### Model Definition
```ruby
# app/models/custom_field.rb
class CustomField < ApplicationRecord
  belongs_to :project
  has_many :custom_field_values, dependent: :destroy
  
  # Enums
  enum field_type: { 
    text: 0, 
    email: 1, 
    phone: 2, 
    url: 3,
    number: 4,
    select: 5, 
    checkbox: 6, 
    textarea: 7,
    date: 8,
    radio: 9
  }
  
  # Validations
  validates :name, presence: true, 
            format: { with: /\A[a-z_][a-z0-9_]*\z/, message: "only lowercase letters, numbers, and underscores" },
            uniqueness: { scope: :project_id }
  validates :label, presence: true, length: { minimum: 1, maximum: 100 }
  validates :field_type, presence: true
  validates :options, presence: true, if: -> { select? || radio? }
  validates :display_order, presence: true, numericality: { greater_than_or_equal_to: 0 }
  
  # Callbacks
  before_validation :normalize_name
  
  # Scopes
  scope :active, -> { where(active: true) }
  scope :required, -> { where(required: true) }
  scope :ordered, -> { order(:display_order, :created_at) }
  scope :for_summary, -> { where(show_in_summary: true) }
  
  # Validation Rules
  def validation_rules_hash
    rules = {}
    rules[:presence] = true if required?
    
    case field_type
    when 'email'
      rules[:format] = { with: URI::MailTo::EMAIL_REGEXP }
    when 'phone'
      rules[:format] = { with: /\A[\+]?[1-9][\d\s\-\(\)]{7,}\z/ }
    when 'url'
      rules[:url] = true
    when 'number'
      rules[:numericality] = true
    end
    
    # Custom validation rules from JSON
    if validation_rules.present?
      begin
        custom_rules = JSON.parse(validation_rules)
        rules.merge!(custom_rules.symbolize_keys)
      rescue JSON::ParserError
        # Ignore invalid JSON
      end
    end
    
    rules
  end
  
  # Value Processing
  def process_value(raw_value)
    return nil if raw_value.blank?
    
    case field_type
    when 'email'
      raw_value.to_s.downcase.strip
    when 'phone'
      raw_value.to_s.gsub(/[^\d\+]/, '')
    when 'number'
      raw_value.to_f
    when 'checkbox'
      ['1', 'true', 'yes', 'on'].include?(raw_value.to_s.downcase)
    else
      raw_value.to_s.strip
    end
  end
  
  # Validation
  def validate_value(value)
    errors = []
    rules = validation_rules_hash
    
    # Required validation
    if rules[:presence] && value.blank?
      errors << "#{label} is required"
    end
    
    return errors if value.blank?
    
    # Format validations
    case field_type
    when 'email'
      unless value.match?(URI::MailTo::EMAIL_REGEXP)
        errors << "#{label} must be a valid email address"
      end
    when 'phone'
      unless value.match?(/\A[\+]?[1-9][\d\s\-\(\)]{7,}\z/)
        errors << "#{label} must be a valid phone number"
      end
    when 'url'
      begin
        uri = URI.parse(value)
        unless uri.is_a?(URI::HTTP) || uri.is_a?(URI::HTTPS)
          errors << "#{label} must be a valid URL"
        end
      rescue URI::InvalidURIError
        errors << "#{label} must be a valid URL"
      end
    when 'select', 'radio'
      unless options.include?(value.to_s)
        errors << "#{label} must be one of: #{options.join(', ')}"
      end
    end
    
    errors
  end
  
  # Conditional Logic
  def should_show?(field_values = {})
    return true if show_conditions.blank?
    
    show_conditions.all? do |condition|
      field_name = condition['field']
      operator = condition['operator']
      expected_value = condition['value']
      actual_value = field_values[field_name]
      
      case operator
      when 'equals'
        actual_value == expected_value
      when 'not_equals'
        actual_value != expected_value
      when 'contains'
        actual_value.to_s.include?(expected_value.to_s)
      when 'not_empty'
        actual_value.present?
      when 'empty'
        actual_value.blank?
      else
        true
      end
    end
  end
  
  private
  
  def normalize_name
    self.name = name.to_s.downcase.strip.gsub(/[^a-z0-9_]/, '_') if name.present?
  end
end

# app/models/custom_field_value.rb
class CustomFieldValue < ApplicationRecord
  belongs_to :waitlist_entry
  belongs_to :custom_field
  
  # Validations
  validate :value_matches_field_requirements
  
  # Callbacks
  before_save :process_value
  
  private
  
  def process_value
    self.value = custom_field.process_value(value)
  end
  
  def value_matches_field_requirements
    validation_errors = custom_field.validate_value(value)
    validation_errors.each { |error| errors.add(:value, error) }
  end
end
```

#### Business Rules
- Field names must be unique per project and follow variable naming conventions
- Options are required for select and radio field types
- Conditional logic supports basic operators for field visibility
- Values are processed and validated according to field type

---

### 5. EmailSequence & EmailTemplate Models
**Purpose**: Automated email communication system

#### Database Schema
```ruby
# Migration: create_email_sequences.rb
class CreateEmailSequences < ActiveRecord::Migration[7.0]
  def change
    create_table :email_sequences do |t|
      t.references :project, null: false, foreign_key: true
      
      # Sequence Definition
      t.string :name, null: false
      t.text :description
      t.integer :trigger_event, null: false
      t.integer :status, default: 0
      
      # Targeting
      t.json :targeting_rules, default: {}
      t.integer :max_sends_per_entry, default: 1
      
      # Tracking
      t.integer :total_sends, default: 0
      t.integer :total_opens, default: 0
      t.integer :total_clicks, default: 0
      
      t.timestamps
    end
    
    add_index :email_sequences, :project_id
    add_index :email_sequences, :trigger_event
    add_index :email_sequences, :status
    add_index :email_sequences, [:project_id, :trigger_event, :status]
  end
end

# Migration: create_email_templates.rb
class CreateEmailTemplates < ActiveRecord::Migration[7.0]
  def change
    create_table :email_templates do |t|
      t.references :email_sequence, null: false, foreign_key: true
      
      # Template Content
      t.string :subject, null: false
      t.text :body, null: false
      t.text :plain_text_body
      
      # Sequence Configuration
      t.integer :sequence_position, null: false
      t.integer :delay_days, default: 0
      t.integer :delay_hours, default: 0
      t.boolean :active, default: true
      
      # Send Time Optimization
      t.json :send_time_preferences, default: {}
      
      # Tracking
      t.integer :sends_count, default: 0
      t.integer :opens_count, default: 0
      t.integer :clicks_count, default: 0
      t.decimal :open_rate, precision: 5, scale: 2, default: 0.0
      t.decimal :click_rate, precision: 5, scale: 2, default: 0.0
      
      t.timestamps
    end
    
    add_index :email_templates, :email_sequence_id
    add_index :email_templates, [:email_sequence_id, :sequence_position], 
              unique: true, name: 'index_email_templates_on_sequence_and_position'
    add_index :email_templates, :active
  end
end

# Migration: create_email_deliveries.rb
class CreateEmailDeliveries < ActiveRecord::Migration[7.0]
  def change
    create_table :email_deliveries do |t|
      t.references :waitlist_entry, null: false, foreign_key: true
      t.references :email_template, null: false, foreign_key: true
      
      # Delivery Status
      t.integer :status, default: 0
      t.datetime :sent_at
      t.datetime :delivered_at
      t.datetime :opened_at
      t.datetime :clicked_at
      t.datetime :bounced_at
      t.datetime :complained_at
      
      # External IDs
      t.string :external_id
      t.string :message_id
      
      # Error Tracking
      t.text :error_message
      t.integer :retry_count, default: 0
      
      t.timestamps
    end
    
    add_index :email_deliveries, [:waitlist_entry_id, :email_template_id], 
              unique: true, name: 'index_email_deliveries_on_entry_and_template'
    add_index :email_deliveries, :status
    add_index :email_deliveries, :sent_at
    add_index :email_deliveries, :external_id
  end
end
```

#### Model Definitions
```ruby
# app/models/email_sequence.rb
class EmailSequence < ApplicationRecord
  belongs_to :project
  has_many :email_templates, dependent: :destroy
  
  # Enums
  enum trigger_event: { 
    welcome: 0, 
    confirmed: 1, 
    weekly_update: 2, 
    launch_reminder: 3,
    milestone_reached: 4,
    referral_reward: 5
  }
  
  enum status: { draft: 0, active: 1, paused: 2, archived: 3 }
  
  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :trigger_event, presence: true
  validates :max_sends_per_entry, presence: true, 
            numericality: { greater_than: 0, less_than_or_equal_to: 10 }
  
  # Scopes
  scope :active_sequences, -> { where(status: :active) }
  scope :for_trigger, ->(trigger) { where(trigger_event: trigger) }
  
  # Statistics
  def calculate_stats!
    templates = email_templates.includes(:email_deliveries)
    
    self.total_sends = templates.sum(&:sends_count)
    self.total_opens = templates.sum(&:opens_count)
    self.total_clicks = templates.sum(&:clicks_count)
    save!
  end
  
  def open_rate
    return 0.0 if total_sends.zero?
    (total_opens.to_f / total_sends * 100).round(2)
  end
  
  def click_rate
    return 0.0 if total_sends.zero?
    (total_clicks.to_f / total_sends * 100).round(2)
  end
  
  # Targeting
  def matches_entry?(waitlist_entry)
    return true if targeting_rules.blank?
    
    targeting_rules.all? do |rule|
      field = rule['field']
      operator = rule['operator']
      value = rule['value']
      
      case field
      when 'status'
        waitlist_entry.status == value
      when 'confirmed_days_ago'
        return false unless waitlist_entry.confirmed?
        days_ago = (Time.current - waitlist_entry.confirmed_at) / 1.day
        compare_number(days_ago, operator, value.to_f)
      when 'referral_count'
        compare_number(waitlist_entry.referral_count, operator, value.to_i)
      else
        # Custom field matching
        entry_value = waitlist_entry.custom_field_value(field)
        compare_values(entry_value, operator, value)
      end
    end
  end
  
  private
  
  def compare_number(actual, operator, expected)
    case operator
    when 'gt' then actual > expected
    when 'gte' then actual >= expected
    when 'lt' then actual < expected
    when 'lte' then actual <= expected
    when 'eq' then actual == expected
    else false
    end
  end
  
  def compare_values(actual, operator, expected)
    case operator
    when 'equals' then actual == expected
    when 'not_equals' then actual != expected
    when 'contains' then actual.to_s.include?(expected.to_s)
    when 'not_empty' then actual.present?
    when 'empty' then actual.blank?
    else false
    end
  end
end

# app/models/email_template.rb
class EmailTemplate < ApplicationRecord
  belongs_to :email_sequence
  has_many :email_deliveries, dependent: :destroy
  
  # Validations
  validates :subject, presence: true, length: { minimum: 5, maximum: 200 }
  validates :body, presence: true, length: { minimum: 10, maximum: 50_000 }
  validates :sequence_position, presence: true, 
            uniqueness: { scope: :email_sequence_id },
            numericality: { greater_than: 0 }
  validates :delay_days, presence: true, 
            numericality: { greater_than_or_equal_to: 0, less_than: 365 }
  validates :delay_hours, presence: true,
            numericality: { greater_than_or_equal_to: 0, less_than: 24 }
  
  # Scopes
  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(:sequence_position) }
  
  # Content Processing
  def personalized_subject(waitlist_entry)
    interpolate_variables(subject, waitlist_entry)
  end
  
  def personalized_body(waitlist_entry)
    interpolate_variables(body, waitlist_entry)
  end
  
  def personalized_plain_text(waitlist_entry)
    return nil if plain_text_body.blank?
    interpolate_variables(plain_text_body, waitlist_entry)
  end
  
  # Delivery Scheduling
  def delivery_delay
    delay_days.days + delay_hours.hours
  end
  
  def optimal_send_time(waitlist_entry)
    base_time = Time.current + delivery_delay
    
    # Apply send time preferences
    if send_time_preferences.present?
      preferred_hour = send_time_preferences['hour']&.to_i
      preferred_timezone = send_time_preferences['timezone']
      
      if preferred_hour && preferred_timezone
        tz = ActiveSupport::TimeZone[preferred_timezone]
        local_time = base_time.in_time_zone(tz)
        optimized_time = local_time.change(hour: preferred_hour, min: 0, sec: 0)
        
        # If the time has passed today, schedule for tomorrow
        optimized_time += 1.day if optimized_time <= Time.current
        
        return optimized_time.utc
      end
    end
    
    base_time
  end
  
  # Statistics
  def calculate_stats!
    deliveries = email_deliveries.includes(:waitlist_entry)
    
    self.sends_count = deliveries.sent.count
    self.opens_count = deliveries.opened.count
    self.clicks_count = deliveries.clicked.count
    
    self.open_rate = sends_count > 0 ? (opens_count.to_f / sends_count * 100).round(2) : 0.0
    self.click_rate = sends_count > 0 ? (clicks_count.to_f / sends_count * 100).round(2) : 0.0
    
    save!
  end
  
  private
  
  def interpolate_variables(text, entry)
    text.gsub(/\{\{(\w+)\}\}/) do |match|
      variable = $1
      case variable
      when 'name' then entry.display_name
      when 'email' then entry.email
      when 'position' then entry.display_position.to_s
      when 'project_name' then entry.project.name
      when 'project_url' then entry.project.public_url
      when 'referral_url' then entry.referral_url
      when 'referral_code' then entry.referral_code
      when 'progress_percentage' then entry.project.progress_percentage.to_s
      when 'confirmed_signups' then entry.project.confirmed_signups.to_s
      when 'launch_threshold' then entry.project.launch_threshold.to_s
      else
        # Try custom field
        entry.custom_field_value(variable) || match
      end
    end
  end
end

# app/models/email_delivery.rb
class EmailDelivery < ApplicationRecord
  belongs_to :waitlist_entry
  belongs_to :email_template
  
  # Enums
  enum status: {
    pending: 0,
    sent: 1,
    delivered: 2,
    opened: 3,
    clicked: 4,
    bounced: 5,
    complained: 6,
    failed: 7
  }
  
  # Validations
  validates :retry_count, presence: true,
            numericality: { greater_than_or_equal_to: 0, less_than: 5 }
  
  # Scopes
  scope :successful, -> { where(status: [:sent, :delivered, :opened, :clicked]) }
  scope :failed, -> { where(status: [:bounced, :complained, :failed]) }
  scope :recent, -> { order(created_at: :desc) }
  
  # Status Updates
  def mark_sent!(external_id: nil, message_id: nil)
    update!(
      status: :sent,
      sent_at: Time.current,
      external_id: external_id,
      message_id: message_id
    )
  end
  
  def mark_delivered!
    update!(status: :delivered, delivered_at: Time.current)
  end
  
  def mark_opened!
    return if opened?
    update!(status: :opened, opened_at: Time.current)
    email_template.increment!(:opens_count)
  end
  
  def mark_clicked!
    mark_opened! unless opened?
    update!(status: :clicked, clicked_at: Time.current) unless clicked?
    email_template.increment!(:clicks_count) unless clicked?
  end
  
  def mark_bounced!(error_message = nil)
    update!(
      status: :bounced,
      bounced_at: Time.current,
      error_message: error_message
    )
  end
  
  def mark_complained!
    update!(status: :complained, complained_at: Time.current)
  end
  
  def mark_failed!(error_message)
    update!(
      status: :failed,
      error_message: error_message,
      retry_count: retry_count + 1
    )
  end
  
  # Retry Logic
  def can_retry?
    failed? && retry_count < 3
  end
  
  def retry_delay
    (retry_count + 1) * 30.minutes
  end
end
```

#### Business Rules
- Email templates must have unique sequence positions within a sequence
- Delivery delays support both days and hours for fine-grained control
- Variable interpolation supports project, entry, and custom field data
- Email deliveries track the complete lifecycle from send to engagement

---

### 6. AnalyticsEvent Model
**Purpose**: Comprehensive event tracking for performance insights

#### Database Schema
```ruby
# Migration: create_analytics_events.rb
class CreateAnalyticsEvents < ActiveRecord::Migration[7.0]
  def change
    create_table :analytics_events do |t|
      t.references :project, null: false, foreign_key: true
      t.references :waitlist_entry, null: true, foreign_key: true
      
      # Event Data
      t.integer :event_type, null: false
      t.datetime :occurred_at, null: false
      t.string :session_id
      
      # Request Data
      t.string :ip_address
      t.string :user_agent
      t.string :referrer
      t.string :page_url
      
      # Attribution Data
      t.string :utm_source
      t.string :utm_medium
      t.string :utm_campaign
      t.string :utm_content
      t.string :utm_term
      
      # Additional Context
      t.json :metadata, default: {}
      t.string :country_code
      t.string :city
      t.string :device_type
      t.string :browser
      t.string :os
      
      t.timestamps
    end
    
    add_index :analytics_events, :project_id
    add_index :analytics_events, :event_type
    add_index :analytics_events, :occurred_at
    add_index :analytics_events, :session_id
    add_index :analytics_events, [:project_id, :event_type, :occurred_at], 
              name: 'index_analytics_events_on_project_type_time'
    add_index :analytics_events, [:project_id, :session_id, :occurred_at],
              name: 'index_analytics_events_on_project_session_time'
  end
end
```

#### Model Definition
```ruby
# app/models/analytics_event.rb
class AnalyticsEvent < ApplicationRecord
  belongs_to :project
  belongs_to :waitlist_entry, optional: true
  
  # Enums
  enum event_type: {
    page_view: 0,
    form_view: 1,
    form_focus: 2,
    form_submit_attempt: 3,
    form_submit_success: 4,
    form_submit_error: 5,
    email_confirmation_sent: 6,
    email_confirmed: 7,
    email_opened: 8,
    email_clicked: 9,
    referral_link_generated: 10,
    referral_link_clicked: 11,
    social_share: 12,
    custom_event: 99
  }
  
  # Validations
  validates :event_type, presence: true
  validates :occurred_at, presence: true
  
  # Scopes
  scope :recent, -> { order(occurred_at: :desc) }
  scope :for_date_range, ->(start_date, end_date) { 
    where(occurred_at: start_date..end_date) 
  }
  scope :by_session, ->(session_id) { where(session_id: session_id) }
  scope :with_entry, -> { where.not(waitlist_entry_id: nil) }
  
  # Aggregation Methods
  def self.conversion_funnel(project, date_range = 30.days.ago..Time.current)
    events = for_date_range(date_range.begin, date_range.end)
             .where(project: project)
    
    page_views = events.page_view.count
    form_views = events.form_view.count
    form_attempts = events.form_submit_attempt.count
    form_successes = events.form_submit_success.count
    confirmations = events.email_confirmed.count
    
    {
      page_views: page_views,
      form_views: form_views,
      form_attempts: form_attempts,
      form_successes: form_successes,
      confirmations: confirmations,
      page_to_form_rate: calculate_rate(form_views, page_views),
      form_to_attempt_rate: calculate_rate(form_attempts, form_views),
      attempt_to_success_rate: calculate_rate(form_successes, form_attempts),
      success_to_confirmation_rate: calculate_rate(confirmations, form_successes),
      overall_conversion_rate: calculate_rate(confirmations, page_views)
    }
  end
  
  def self.traffic_sources(project, date_range = 30.days.ago..Time.current)
    for_date_range(date_range.begin, date_range.end)
      .where(project: project, event_type: :page_view)
      .group(:utm_source)
      .group(:utm_medium)
      .count
      .transform_keys { |key| key.compact.join(' / ').presence || 'Direct' }
  end
  
  def self.geographic_distribution(project, date_range = 30.days.ago..Time.current)
    for_date_range(date_range.begin, date_range.end)
      .where(project: project, event_type: :page_view)
      .where.not(country_code: nil)
      .group(:country_code)
      .count
  end
  
  def self.device_breakdown(project, date_range = 30.days.ago..Time.current)
    for_date_range(date_range.begin, date_range.end)
      .where(project: project, event_type: :page_view)
      .group(:device_type)
      .count
  end
  
  def self.hourly_trends(project, date_range = 7.days.ago..Time.current)
    for_date_range(date_range.begin, date_range.end)
      .where(project: project, event_type: :page_view)
      .group_by_hour(:occurred_at)
      .count
  end
  
  def self.daily_trends(project, date_range = 30.days.ago..Time.current)
    for_date_range(date_range.begin, date_range.end)
      .where(project: project, event_type: :form_submit_success)
      .group_by_day(:occurred_at)
      .count
  end
  
  # Session Analysis
  def self.session_stats(project, date_range = 30.days.ago..Time.current)
    sessions = for_date_range(date_range.begin, date_range.end)
               .where(project: project)
               .group(:session_id)
               .group_by(&:session_id)
    
    session_durations = sessions.map do |session_id, events|
      next 0 if events.empty?
      
      sorted_events = events.sort_by(&:occurred_at)
      duration = sorted_events.last.occurred_at - sorted_events.first.occurred_at
      [session_id, duration.to_i]
    end.to_h
    
    {
      total_sessions: sessions.count,
      average_duration: session_durations.values.sum / sessions.count.to_f,
      bounce_rate: calculate_bounce_rate(sessions)
    }
  end
  
  private
  
  def self.calculate_rate(numerator, denominator)
    return 0.0 if denominator.zero?
    (numerator.to_f / denominator * 100).round(2)
  end
  
  def self.calculate_bounce_rate(sessions)
    single_event_sessions = sessions.count { |_, events| events.count == 1 }
    return 0.0 if sessions.empty?
    (single_event_sessions.to_f / sessions.count * 100).round(2)
  end
end
```

#### Business Rules
- All events must have occurred_at timestamp for temporal analysis
- Session-based analysis enables user journey tracking
- Geographic and device data enriches audience insights
- Conversion funnel tracks complete user journey from view to confirmation

---

## Relationships Summary

```ruby
# Core Entity Relationships
User (1) → (many) Project
Project (1) → (many) WaitlistEntry
Project (1) → (many) CustomField
Project (1) → (many) EmailSequence
Project (1) → (many) AnalyticsEvent

# Email System Relationships
EmailSequence (1) → (many) EmailTemplate
EmailTemplate (1) → (many) EmailDelivery
WaitlistEntry (1) → (many) EmailDelivery

# Custom Fields Relationships
CustomField (1) → (many) CustomFieldValue
WaitlistEntry (1) → (many) CustomFieldValue

# Referral System Relationships
WaitlistEntry (1) → (many) WaitlistEntry (as referrer)

# Analytics Relationships
Project (1) → (many) AnalyticsEvent
WaitlistEntry (1) → (many) AnalyticsEvent (optional)
```

## Database Indexes Strategy

### Performance-Critical Indexes
```sql
-- User queries
CREATE INDEX idx_users_subscription_tier ON users(subscription_tier);
CREATE INDEX idx_users_last_active ON users(last_active_at);

-- Project queries
CREATE INDEX idx_projects_user_status ON projects(user_id, status);
CREATE INDEX idx_projects_slug ON projects(slug);
CREATE INDEX idx_projects_status ON projects(status);

-- Waitlist entry queries
CREATE INDEX idx_waitlist_entries_project_email ON waitlist_entries(project_id, email);
CREATE INDEX idx_waitlist_entries_project_status_created ON waitlist_entries(project_id, status, created_at);
CREATE INDEX idx_waitlist_entries_confirmation_token ON waitlist_entries(confirmation_token);
CREATE INDEX idx_waitlist_entries_referral_code ON waitlist_entries(referral_code);

-- Analytics queries
CREATE INDEX idx_analytics_events_project_type_time ON analytics_events(project_id, event_type, occurred_at);
CREATE INDEX idx_analytics_events_session_time ON analytics_events(session_id, occurred_at);

-- Email system queries
CREATE INDEX idx_email_deliveries_template_status ON email_deliveries(email_template_id, status);
CREATE INDEX idx_email_deliveries_entry_template ON email_deliveries(waitlist_entry_id, email_template_id);
```

### Composite Indexes for Complex Queries
```sql
-- Dashboard performance
CREATE INDEX idx_projects_user_created ON projects(user_id, created_at DESC);
CREATE INDEX idx_waitlist_entries_project_confirmed_created ON waitlist_entries(project_id, status, created_at DESC) WHERE status = 1;

-- Analytics performance
CREATE INDEX idx_analytics_events_project_date_type ON analytics_events(project_id, occurred_at, event_type);
CREATE INDEX idx_analytics_events_utm_tracking ON analytics_events(project_id, utm_source, utm_medium, occurred_at);
```

This comprehensive domain model provides the foundation for a scalable, maintainable WaitlistBuilder application with clear business rules, proper relationships, and performance optimizations.