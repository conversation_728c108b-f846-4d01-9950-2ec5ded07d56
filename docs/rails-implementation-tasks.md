# WaitlistBuilder: Rails Implementation Tasks

## Overview

This document breaks down the PRD into specific, implementable Rails tasks organized by business value and development phases. Each task includes clear technical direction, acceptance criteria, and estimated effort.

---

## Phase 1: Core MVP Foundation (Weeks 1-3)
*Business Value: Enable users to create and manage basic waitlists*

### Task 1.1: User Authentication System
**Business Value**: Users can securely register and access their accounts  
**Effort**: 5 days  
**Priority**: P0 (Blocker)

#### Technical Implementation
```ruby
# Gemfile additions
gem 'devise'
gem 'omniauth'
gem 'omniauth-google-oauth2'
gem 'omniauth-rails_csrf_protection'

# Rails generators to run
rails generate devise:install
rails generate devise User
rails generate devise:views
```

#### Database Schema
```ruby
# Migration: devise_create_users.rb
class DeviseCreateUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :users do |t|
      t.string :email,              null: false, default: ""
      t.string :encrypted_password, null: false, default: ""
      t.string :name,               null: false
      t.integer :subscription_tier, default: 0
      t.datetime :confirmed_at
      t.string :confirmation_token
      t.timestamps null: false
    end
    
    add_index :users, :email,                unique: true
    add_index :users, :confirmation_token,   unique: true
  end
end
```

#### Models
```ruby
# app/models/user.rb
class User < ApplicationRecord
  devise :database_authenticatable, :registerable, :recoverable, 
         :rememberable, :validatable, :confirmable

  enum subscription_tier: { free: 0, starter: 1, growth: 2, pro: 3, enterprise: 4 }
  
  has_many :projects, dependent: :destroy
  has_many :waitlist_entries, through: :projects
  
  validates :name, presence: true
  
  def can_create_project?
    case subscription_tier
    when 'free' then projects.count < 1
    when 'starter' then projects.count < 3
    when 'growth' then projects.count < 10
    else true
    end
  end
  
  def total_signups
    waitlist_entries.count
  end
end
```

#### Controllers
```ruby
# app/controllers/application_controller.rb
class ApplicationController < ActionController::Base
  protect_from_forgery with: :exception
  before_action :authenticate_user!, unless: :public_controller?
  before_action :configure_permitted_parameters, if: :devise_controller?

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [:name])
    devise_parameter_sanitizer.permit(:account_update, keys: [:name])
  end

  private

  def public_controller?
    controller_name == 'waitlists' && controller_path.start_with?('public/')
  end
end
```

#### Acceptance Criteria
- [ ] Users can register with email and password
- [ ] Email confirmation workflow works
- [ ] Password reset functionality works
- [ ] Google OAuth integration works
- [ ] User sessions persist correctly
- [ ] Subscription tier logic enforces project limits

---

### Task 1.2: Project Management Core
**Business Value**: Users can create and organize waitlist projects  
**Effort**: 4 days  
**Priority**: P0 (Blocker)

#### Database Schema
```ruby
# Migration: create_projects.rb
class CreateProjects < ActiveRecord::Migration[7.0]
  def change
    create_table :projects do |t|
      t.references :user, null: false, foreign_key: true
      t.string :name, null: false
      t.string :slug, null: false
      t.text :description
      t.string :website_url
      t.string :logo_url
      t.text :thank_you_message
      t.integer :status, default: 0
      t.integer :launch_threshold, default: 100
      t.boolean :requires_confirmation, default: true
      t.boolean :collect_names, default: true
      t.boolean :collect_companies, default: false
      t.json :branding_colors, default: {}
      t.timestamps
    end
    
    add_index :projects, :slug, unique: true
    add_index :projects, :user_id
    add_index :projects, :status
  end
end
```

#### Models
```ruby
# app/models/project.rb
class Project < ApplicationRecord
  belongs_to :user
  has_many :waitlist_entries, dependent: :destroy
  has_many :custom_fields, dependent: :destroy
  has_many :email_sequences, dependent: :destroy
  
  enum status: { draft: 0, active: 1, closed: 2, archived: 3 }
  
  validates :name, presence: true
  validates :slug, presence: true, uniqueness: true, 
            format: { with: /\A[a-z0-9\-]+\z/ }
  validates :description, presence: true
  
  before_validation :generate_slug, if: -> { slug.blank? && name.present? }
  
  scope :publicly_visible, -> { where(status: :active) }
  
  def signup_count
    waitlist_entries.confirmed.count
  end
  
  def conversion_rate
    return 0 if page_views.zero?
    (signup_count.to_f / page_views * 100).round(2)
  end
  
  private
  
  def generate_slug
    self.slug = name.parameterize
  end
end
```

#### Controllers
```ruby
# app/controllers/projects_controller.rb
class ProjectsController < ApplicationController
  before_action :set_project, only: [:show, :edit, :update, :destroy]
  before_action :ensure_project_owner, only: [:show, :edit, :update, :destroy]
  before_action :check_project_limit, only: [:create]
  
  def index
    @projects = current_user.projects.includes(:waitlist_entries)
                           .order(created_at: :desc)
                           .page(params[:page])
  end
  
  def show
    @waitlist_entries = @project.waitlist_entries
                               .order(created_at: :desc)
                               .page(params[:page])
  end
  
  def new
    @project = current_user.projects.build
  end
  
  def create
    @project = current_user.projects.build(project_params)
    
    if @project.save
      redirect_to @project, notice: 'Project created successfully!'
    else
      render :new, status: :unprocessable_entity
    end
  end
  
  def edit
  end
  
  def update
    if @project.update(project_params)
      redirect_to @project, notice: 'Project updated successfully!'
    else
      render :edit, status: :unprocessable_entity
    end
  end
  
  def destroy
    @project.destroy
    redirect_to projects_path, notice: 'Project deleted.'
  end
  
  private
  
  def set_project
    @project = current_user.projects.find(params[:id])
  end
  
  def project_params
    params.require(:project).permit(:name, :description, :website_url, 
                                   :logo_url, :thank_you_message, 
                                   :launch_threshold, :requires_confirmation,
                                   :collect_names, :collect_companies,
                                   branding_colors: {})
  end
  
  def ensure_project_owner
    redirect_to projects_path unless @project.user == current_user
  end
  
  def check_project_limit
    unless current_user.can_create_project?
      redirect_to projects_path, 
                  alert: 'Project limit reached for your subscription.'
    end
  end
end
```

#### Routes
```ruby
# config/routes.rb
Rails.application.routes.draw do
  devise_for :users
  root 'dashboard#index'
  
  resources :projects do
    resources :waitlist_entries, except: [:edit, :update]
    member do
      get :analytics
      patch :toggle_status
    end
  end
end
```

#### Acceptance Criteria
- [ ] Users can create projects with required fields
- [ ] Project slugs are automatically generated and unique
- [ ] Users can only access their own projects
- [ ] Project limits are enforced by subscription tier
- [ ] Projects can be edited and deleted
- [ ] All project operations work via web interface

---

### Task 1.3: Waitlist Entry System
**Business Value**: Visitors can join waitlists and users can manage subscribers  
**Effort**: 5 days  
**Priority**: P0 (Blocker)

#### Database Schema
```ruby
# Migration: create_waitlist_entries.rb
class CreateWaitlistEntries < ActiveRecord::Migration[7.0]
  def change
    create_table :waitlist_entries do |t|
      t.references :project, null: false, foreign_key: true
      t.string :email, null: false
      t.string :name
      t.string :company
      t.text :message
      t.integer :status, default: 0
      t.string :confirmation_token
      t.datetime :confirmed_at
      t.string :ip_address
      t.string :user_agent
      t.string :referrer
      t.string :utm_source
      t.string :utm_medium
      t.string :utm_campaign
      t.timestamps
    end
    
    add_index :waitlist_entries, [:project_id, :email], unique: true
    add_index :waitlist_entries, :confirmation_token
    add_index :waitlist_entries, :status
    add_index :waitlist_entries, :created_at
  end
end
```

#### Models
```ruby
# app/models/waitlist_entry.rb
class WaitlistEntry < ApplicationRecord
  belongs_to :project
  has_many :custom_field_values, dependent: :destroy
  
  enum status: { pending: 0, confirmed: 1, unsubscribed: 2, bounced: 3 }
  
  validates :email, presence: true, 
            format: { with: URI::MailTo::EMAIL_REGEXP },
            uniqueness: { scope: :project_id }
  
  before_create :generate_confirmation_token
  after_create :send_confirmation_email, if: -> { project.requires_confirmation? }
  
  scope :recent, -> { order(created_at: :desc) }
  scope :this_week, -> { where(created_at: 1.week.ago..Time.current) }
  
  def position_in_queue
    project.waitlist_entries.confirmed
           .where('created_at <= ?', created_at)
           .count
  end
  
  def confirm!
    update!(status: :confirmed, confirmed_at: Time.current)
  end
  
  private
  
  def generate_confirmation_token
    self.confirmation_token = SecureRandom.urlsafe_base64(32)
  end
  
  def send_confirmation_email
    WaitlistMailer.confirmation_email(self).deliver_later
  end
end
```

#### Service Objects
```ruby
# app/services/waitlist_signup_service.rb
class WaitlistSignupService
  attr_reader :project, :params, :waitlist_entry, :errors
  
  def initialize(project, params)
    @project = project
    @params = params.with_indifferent_access
    @errors = []
  end
  
  def call
    ActiveRecord::Base.transaction do
      create_waitlist_entry
      process_custom_fields if custom_fields_present?
      track_signup_event
      
      OpenStruct.new(
        success?: @errors.empty?,
        waitlist_entry: @waitlist_entry,
        errors: @errors
      )
    end
  rescue ActiveRecord::RecordInvalid => e
    @errors.concat(e.record.errors.full_messages)
    OpenStruct.new(success?: false, errors: @errors)
  end
  
  private
  
  def create_waitlist_entry
    @waitlist_entry = @project.waitlist_entries.build(entry_params)
    @waitlist_entry.save!
  end
  
  def process_custom_fields
    @params[:custom_fields]&.each do |field_id, value|
      next if value.blank?
      
      custom_field = @project.custom_fields.find_by(id: field_id)
      next unless custom_field
      
      @waitlist_entry.custom_field_values.create!(
        custom_field: custom_field,
        value: value.to_s.strip
      )
    end
  end
  
  def track_signup_event
    # Analytics tracking implementation
  end
  
  def entry_params
    @params.slice(:email, :name, :company, :message)
           .merge(
             ip_address: @params[:ip_address],
             user_agent: @params[:user_agent],
             referrer: @params[:referrer],
             utm_source: @params[:utm_source],
             utm_medium: @params[:utm_medium],
             utm_campaign: @params[:utm_campaign]
           )
  end
  
  def custom_fields_present?
    @params[:custom_fields].present?
  end
end
```

#### Controllers
```ruby
# app/controllers/waitlist_entries_controller.rb
class WaitlistEntriesController < ApplicationController
  before_action :set_project
  before_action :set_waitlist_entry, only: [:show, :destroy, :confirm]
  before_action :ensure_project_owner
  
  def index
    @waitlist_entries = @project.waitlist_entries
                               .includes(:custom_field_values)
                               .order(created_at: :desc)
                               .page(params[:page])
  end
  
  def show
  end
  
  def destroy
    @waitlist_entry.destroy
    redirect_to project_waitlist_entries_path(@project)
  end
  
  def confirm
    @waitlist_entry.confirm!
    redirect_to project_waitlist_entries_path(@project)
  end
  
  private
  
  def set_project
    @project = current_user.projects.find(params[:project_id])
  end
  
  def set_waitlist_entry
    @waitlist_entry = @project.waitlist_entries.find(params[:id])
  end
  
  def ensure_project_owner
    redirect_to projects_path unless @project.user == current_user
  end
end

# app/controllers/public/waitlists_controller.rb
class Public::WaitlistsController < ApplicationController
  skip_before_action :authenticate_user!
  before_action :find_project
  
  def show
    # Display public waitlist page
  end
  
  def create
    result = WaitlistSignupService.new(@project, signup_params).call
    
    if result.success?
      redirect_to public_waitlist_path(@project.slug), 
                  notice: 'Successfully joined the waitlist!'
    else
      redirect_to public_waitlist_path(@project.slug), 
                  alert: result.errors.join(', ')
    end
  end
  
  def confirm
    entry = @project.waitlist_entries.find_by(confirmation_token: params[:token])
    
    if entry&.confirm!
      redirect_to public_waitlist_path(@project.slug), 
                  notice: 'Email confirmed! You\'re on the waitlist.'
    else
      redirect_to public_waitlist_path(@project.slug), 
                  alert: 'Invalid confirmation link.'
    end
  end
  
  private
  
  def find_project
    @project = Project.find_by!(slug: params[:slug], status: :active)
  end
  
  def signup_params
    params.permit(:email, :name, :company, :message, custom_fields: {})
          .merge(
            ip_address: request.remote_ip,
            user_agent: request.user_agent,
            referrer: request.referrer,
            utm_source: params[:utm_source],
            utm_medium: params[:utm_medium],
            utm_campaign: params[:utm_campaign]
          )
  end
end
```

#### Acceptance Criteria
- [ ] Visitors can submit waitlist forms on public pages
- [ ] Email validation prevents duplicate signups per project
- [ ] Confirmation emails are sent when required
- [ ] Users can view and manage their waitlist entries
- [ ] All form data is captured including UTM parameters
- [ ] Position in queue is calculated correctly

---

## Phase 2: Custom Fields & Email System (Week 4)
*Business Value: Users can customize forms and automate email communication*

### Task 2.1: Custom Fields System
**Business Value**: Users can gather specific insights through custom form fields  
**Effort**: 3 days  
**Priority**: P1 (High)

#### Database Schema
```ruby
# Migration: create_custom_fields.rb
class CreateCustomFields < ActiveRecord::Migration[7.0]
  def change
    create_table :custom_fields do |t|
      t.references :project, null: false, foreign_key: true
      t.string :name, null: false
      t.string :label, null: false
      t.integer :field_type, null: false
      t.boolean :required, default: false
      t.json :options, default: []
      t.text :help_text
      t.integer :display_order, default: 0
      t.boolean :active, default: true
      t.timestamps
    end
    
    add_index :custom_fields, :project_id
    add_index :custom_fields, :display_order
  end
end

# Migration: create_custom_field_values.rb
class CreateCustomFieldValues < ActiveRecord::Migration[7.0]
  def change
    create_table :custom_field_values do |t|
      t.references :waitlist_entry, null: false, foreign_key: true
      t.references :custom_field, null: false, foreign_key: true
      t.text :value
      t.timestamps
    end
    
    add_index :custom_field_values, [:waitlist_entry_id, :custom_field_id], 
              unique: true, name: 'index_custom_field_values_on_entry_and_field'
  end
end
```

#### Models
```ruby
# app/models/custom_field.rb
class CustomField < ApplicationRecord
  belongs_to :project
  has_many :custom_field_values, dependent: :destroy
  
  enum field_type: { 
    text: 0, 
    email: 1, 
    phone: 2, 
    select: 3, 
    checkbox: 4, 
    textarea: 5 
  }
  
  validates :name, presence: true
  validates :label, presence: true
  validates :field_type, presence: true
  
  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(:display_order) }
  
  def validation_rules
    rules = {}
    rules[:presence] = true if required?
    
    case field_type
    when 'email'
      rules[:format] = { with: URI::MailTo::EMAIL_REGEXP }
    when 'phone'
      rules[:format] = { with: /\A[\+]?[1-9][\d\s\-\(\)]{7,}\z/ }
    end
    
    rules
  end
end

# app/models/custom_field_value.rb
class CustomFieldValue < ApplicationRecord
  belongs_to :waitlist_entry
  belongs_to :custom_field
  
  validates :value, presence: true, if: -> { custom_field.required? }
  validate :value_matches_field_type
  
  private
  
  def value_matches_field_type
    return unless value.present?
    
    case custom_field.field_type
    when 'email'
      unless value.match?(URI::MailTo::EMAIL_REGEXP)
        errors.add(:value, 'must be a valid email')
      end
    when 'phone'
      unless value.match?(/\A[\+]?[1-9][\d\s\-\(\)]{7,}\z/)
        errors.add(:value, 'must be a valid phone number')
      end
    end
  end
end
```

#### Controllers
```ruby
# app/controllers/custom_fields_controller.rb
class CustomFieldsController < ApplicationController
  before_action :set_project
  before_action :set_custom_field, only: [:show, :edit, :update, :destroy, :toggle_active]
  before_action :ensure_project_owner
  
  def index
    @custom_fields = @project.custom_fields.ordered
  end
  
  def show
  end
  
  def new
    @custom_field = @project.custom_fields.build
  end
  
  def create
    @custom_field = @project.custom_fields.build(custom_field_params)
    
    if @custom_field.save
      redirect_to [@project, @custom_field], notice: 'Custom field created!'
    else
      render :new, status: :unprocessable_entity
    end
  end
  
  def edit
  end
  
  def update
    if @custom_field.update(custom_field_params)
      redirect_to [@project, @custom_field], notice: 'Custom field updated!'
    else
      render :edit, status: :unprocessable_entity
    end
  end
  
  def destroy
    @custom_field.destroy
    redirect_to project_custom_fields_path(@project)
  end
  
  def toggle_active
    @custom_field.update!(active: !@custom_field.active?)
    redirect_to project_custom_fields_path(@project)
  end
  
  private
  
  def set_project
    @project = current_user.projects.find(params[:project_id])
  end
  
  def set_custom_field
    @custom_field = @project.custom_fields.find(params[:id])
  end
  
  def custom_field_params
    params.require(:custom_field).permit(:name, :label, :field_type, 
                                         :required, :help_text, :display_order,
                                         options: [])
  end
  
  def ensure_project_owner
    redirect_to projects_path unless @project.user == current_user
  end
end
```

#### Acceptance Criteria
- [ ] Users can create custom fields of different types
- [ ] Custom fields appear on public waitlist forms
- [ ] Field validation works for all field types
- [ ] Custom field values are stored and displayed
- [ ] Fields can be reordered and toggled active/inactive

---

### Task 2.2: Email System Foundation
**Business Value**: Automated email communication with subscribers  
**Effort**: 4 days  
**Priority**: P1 (High)

#### Gemfile
```ruby
gem 'solid_queue'
```

#### Database Schema
```ruby
# Migration: create_email_sequences.rb
class CreateEmailSequences < ActiveRecord::Migration[7.0]
  def change
    create_table :email_sequences do |t|
      t.references :project, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.integer :trigger_event, null: false
      t.integer :status, default: 0
      t.timestamps
    end
    
    add_index :email_sequences, :project_id
    add_index :email_sequences, :trigger_event
    add_index :email_sequences, :status
  end
end

# Migration: create_email_templates.rb
class CreateEmailTemplates < ActiveRecord::Migration[7.0]
  def change
    create_table :email_templates do |t|
      t.references :email_sequence, null: false, foreign_key: true
      t.string :subject, null: false
      t.text :body, null: false
      t.integer :sequence_position, null: false
      t.integer :delay_days, default: 0
      t.boolean :active, default: true
      t.timestamps
    end
    
    add_index :email_templates, :email_sequence_id
    add_index :email_templates, [:email_sequence_id, :sequence_position], 
              unique: true, name: 'index_email_templates_on_sequence_and_position'
  end
end
```

#### Models
```ruby
# app/models/email_sequence.rb
class EmailSequence < ApplicationRecord
  belongs_to :project
  has_many :email_templates, dependent: :destroy
  
  enum trigger_event: { 
    welcome: 0, 
    confirmed: 1, 
    weekly_update: 2, 
    launch_reminder: 3 
  }
  
  enum status: { draft: 0, active: 1, paused: 2 }
  
  validates :name, presence: true
  validates :trigger_event, presence: true
  
  scope :active_sequences, -> { where(status: :active) }
end

# app/models/email_template.rb
class EmailTemplate < ApplicationRecord
  belongs_to :email_sequence
  
  validates :subject, presence: true
  validates :body, presence: true
  validates :sequence_position, presence: true, 
            uniqueness: { scope: :email_sequence_id }
  validates :delay_days, presence: true, 
            numericality: { greater_than_or_equal_to: 0 }
  
  scope :ordered, -> { order(:sequence_position) }
  
  def personalized_subject(waitlist_entry)
    interpolate_variables(subject, waitlist_entry)
  end
  
  def personalized_body(waitlist_entry)
    interpolate_variables(body, waitlist_entry)
  end
  
  private
  
  def interpolate_variables(text, entry)
    text.gsub(/\{\{(\w+)\}\}/) do |match|
      variable = $1
      case variable
      when 'name' then entry.name || 'there'
      when 'email' then entry.email
      when 'position' then entry.position_in_queue.to_s
      when 'project_name' then entry.project.name
      else match
      end
    end
  end
end
```

#### Mailers
```ruby
# app/mailers/waitlist_mailer.rb
class WaitlistMailer < ApplicationMailer
  default from: '<EMAIL>'

  def confirmation_email(waitlist_entry)
    @entry = waitlist_entry
    @project = waitlist_entry.project
    @confirmation_url = confirm_public_waitlist_url(
      @project.slug, 
      token: @entry.confirmation_token
    )
    
    mail(
      to: @entry.email,
      subject: "Please confirm your interest in #{@project.name}"
    )
  end

  def welcome_email(waitlist_entry)
    @entry = waitlist_entry
    @project = waitlist_entry.project
    @position = waitlist_entry.position_in_queue
    
    mail(
      to: @entry.email,
      subject: "Welcome to the #{@project.name} waitlist!"
    )
  end

  def sequence_email(waitlist_entry, email_template)
    @entry = waitlist_entry
    @project = waitlist_entry.project
    @template = email_template
    
    mail(
      to: @entry.email,
      subject: @template.personalized_subject(@entry)
    ) do |format|
      format.html { render inline: @template.personalized_body(@entry) }
    end
  end
end
```

#### Background Jobs
```ruby
# app/jobs/email_sequence_job.rb
class EmailSequenceJob < ApplicationJob
  queue_as :default

  def perform(waitlist_entry, trigger_event)
    sequences = waitlist_entry.project.email_sequences
                              .active_sequences
                              .where(trigger_event: trigger_event)

    sequences.each do |sequence|
      sequence.email_templates.active.ordered.each do |template|
        EmailDeliveryJob.set(wait: template.delay_days.days)
                        .perform_later(waitlist_entry, template)
      end
    end
  end
end

# app/jobs/email_delivery_job.rb
class EmailDeliveryJob < ApplicationJob
  queue_as :default

  def perform(waitlist_entry, email_template)
    return unless waitlist_entry.confirmed?
    
    WaitlistMailer.sequence_email(waitlist_entry, email_template).deliver_now
  end
end
```

#### Acceptance Criteria
- [ ] Email sequences can be created and configured
- [ ] Email templates support variable interpolation
- [ ] Background jobs process email delivery with SolidQueue
- [ ] Confirmation emails are sent automatically
- [ ] Welcome emails trigger after confirmation
- [ ] Email delivery is tracked and logged

---

## Phase 3: Analytics & Dashboard (Week 5)
*Business Value: Users gain insights to optimize their waitlists*

### Task 3.1: Analytics Events System
**Business Value**: Track user behavior and waitlist performance  
**Effort**: 3 days  
**Priority**: P1 (High)

#### Database Schema
```ruby
# Migration: create_analytics_events.rb
class CreateAnalyticsEvents < ActiveRecord::Migration[7.0]
  def change
    create_table :analytics_events do |t|
      t.references :project, null: false, foreign_key: true
      t.references :waitlist_entry, null: true, foreign_key: true
      t.integer :event_type, null: false
      t.datetime :occurred_at, null: false
      t.string :session_id
      t.string :ip_address
      t.string :user_agent
      t.string :referrer
      t.string :utm_source
      t.string :utm_medium
      t.string :utm_campaign
      t.json :metadata, default: {}
      t.timestamps
    end
    
    add_index :analytics_events, :project_id
    add_index :analytics_events, :event_type
    add_index :analytics_events, :occurred_at
    add_index :analytics_events, [:project_id, :event_type, :occurred_at], 
              name: 'index_analytics_events_on_project_type_time'
  end
end
```

#### Models
```ruby
# app/models/analytics_event.rb
class AnalyticsEvent < ApplicationRecord
  belongs_to :project
  belongs_to :waitlist_entry, optional: true
  
  enum event_type: {
    page_view: 0,
    signup_started: 1,
    signup_completed: 2,
    email_opened: 3,
    email_clicked: 4,
    referral_used: 5
  }
  
  validates :event_type, presence: true
  validates :occurred_at, presence: true
  
  scope :recent, -> { order(occurred_at: :desc) }
  scope :for_date_range, ->(start_date, end_date) { 
    where(occurred_at: start_date..end_date) 
  }
  
  def self.conversion_funnel(project, date_range = 7.days.ago..Time.current)
    events = for_date_range(date_range.begin, date_range.end)
             .where(project: project)
    
    {
      page_views: events.page_view.count,
      signup_starts: events.signup_started.count,
      signup_completions: events.signup_completed.count,
      conversion_rate: calculate_conversion_rate(events)
    }
  end
  
  private
  
  def self.calculate_conversion_rate(events)
    page_views = events.page_view.count
    signups = events.signup_completed.count
    
    return 0 if page_views.zero?
    (signups.to_f / page_views * 100).round(2)
  end
end
```

#### Services
```ruby
# app/services/analytics_service.rb
class AnalyticsService
  def initialize(project)
    @project = project
  end
  
  def track_event(event_type, waitlist_entry: nil, metadata: {}, request_data: {})
    @project.analytics_events.create!(
      event_type: event_type,
      waitlist_entry: waitlist_entry,
      occurred_at: Time.current,
      session_id: request_data[:session_id],
      ip_address: request_data[:ip_address],
      user_agent: request_data[:user_agent],
      referrer: request_data[:referrer],
      utm_source: request_data[:utm_source],
      utm_medium: request_data[:utm_medium],
      utm_campaign: request_data[:utm_campaign],
      metadata: metadata
    )
  end
  
  def generate_report(date_range = 30.days.ago..Time.current)
    {
      overview: overview_metrics(date_range),
      traffic_sources: traffic_sources(date_range),
      conversion_funnel: conversion_funnel(date_range),
      signup_trends: signup_trends(date_range)
    }
  end
  
  private
  
  def overview_metrics(date_range)
    events = @project.analytics_events.for_date_range(date_range.begin, date_range.end)
    
    {
      total_page_views: events.page_view.count,
      total_signups: events.signup_completed.count,
      conversion_rate: calculate_conversion_rate(events),
      unique_visitors: events.page_view.distinct.count(:session_id)
    }
  end
  
  def traffic_sources(date_range)
    @project.analytics_events
            .page_view
            .for_date_range(date_range.begin, date_range.end)
            .group(:utm_source)
            .count
  end
  
  def conversion_funnel(date_range)
    AnalyticsEvent.conversion_funnel(@project, date_range)
  end
  
  def signup_trends(date_range)
    @project.analytics_events
            .signup_completed
            .for_date_range(date_range.begin, date_range.end)
            .group_by_day(:occurred_at)
            .count
  end
  
  def calculate_conversion_rate(events)
    page_views = events.page_view.count
    signups = events.signup_completed.count
    
    return 0 if page_views.zero?
    (signups.to_f / page_views * 100).round(2)
  end
end
```

#### API Controllers
```ruby
# app/controllers/api/v1/analytics_events_controller.rb
class Api::V1::AnalyticsEventsController < Api::V1::BaseController
  def create
    project = Project.find_by!(slug: params[:project_slug])
    
    service = AnalyticsService.new(project)
    service.track_event(
      params[:event_type],
      metadata: params[:metadata] || {},
      request_data: {
        session_id: params[:session_id],
        ip_address: request.remote_ip,
        user_agent: request.user_agent,
        referrer: request.referrer,
        utm_source: params[:utm_source],
        utm_medium: params[:utm_medium],
        utm_campaign: params[:utm_campaign]
      }
    )
    
    render json: { status: 'success' }
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Project not found' }, status: :not_found
  end
end
```

#### Acceptance Criteria
- [ ] Analytics events are tracked for all user interactions
- [ ] Conversion funnel calculation works correctly
- [ ] Traffic source attribution is accurate
- [ ] Date range filtering works properly
- [ ] API endpoints track events from public pages

---

### Task 3.2: Dashboard Interface
**Business Value**: Users can visualize performance and make data-driven decisions  
**Effort**: 4 days  
**Priority**: P1 (High)

#### Controllers
```ruby
# app/controllers/dashboard_controller.rb
class DashboardController < ApplicationController
  def index
    @projects = current_user.projects
                           .includes(:waitlist_entries, :analytics_events)
                           .order(created_at: :desc)
                           .limit(6)
    
    @overview_stats = calculate_overview_stats
    @recent_signups = recent_signups_data
    @signup_trends = signup_trends_data
  end
  
  private
  
  def calculate_overview_stats
    {
      total_projects: current_user.projects.count,
      total_signups: current_user.total_signups,
      active_projects: current_user.projects.active.count,
      this_week_signups: current_user.waitlist_entries
                                     .where(created_at: 1.week.ago..Time.current)
                                     .count
    }
  end
  
  def recent_signups_data
    current_user.waitlist_entries
                .includes(:project)
                .confirmed
                .order(created_at: :desc)
                .limit(10)
  end
  
  def signup_trends_data
    current_user.waitlist_entries
                .where(created_at: 30.days.ago..Time.current)
                .group_by_day(:created_at)
                .count
  end
end

# app/controllers/projects_controller.rb (add analytics action)
class ProjectsController < ApplicationController
  # ... existing actions ...
  
  def analytics
    @date_range = parse_date_range
    @analytics_service = AnalyticsService.new(@project)
    @analytics_data = @analytics_service.generate_report(@date_range)
    
    respond_to do |format|
      format.html
      format.json { render json: @analytics_data }
    end
  end
  
  private
  
  def parse_date_range
    start_date = params[:start_date]&.to_date || 30.days.ago.to_date
    end_date = params[:end_date]&.to_date || Date.current
    start_date..end_date
  end
end
```

#### Views with JavaScript Integration
```erb
<!-- app/views/dashboard/index.html.erb -->
<div class="dashboard-container" data-controller="dashboard">
  <!-- Overview Stats -->
  <div class="stats-grid">
    <div class="stat-card">
      <h3>Total Signups</h3>
      <p class="stat-number"><%= @overview_stats[:total_signups] %></p>
    </div>
    <!-- More stat cards... -->
  </div>
  
  <!-- Charts -->
  <div class="charts-container">
    <canvas id="signupTrendsChart" 
            data-chart-data="<%= @signup_trends.to_json %>"
            data-controller="chart"></canvas>
  </div>
  
  <!-- Recent Activity -->
  <div class="recent-activity">
    <% @recent_signups.each do |signup| %>
      <div class="activity-item">
        <%= signup.email %> joined <%= signup.project.name %>
        <span class="timestamp"><%= time_ago_in_words(signup.created_at) %> ago</span>
      </div>
    <% end %>
  </div>
</div>
```

#### Stimulus Controllers
```javascript
// app/javascript/controllers/chart_controller.js
import { Controller } from "@hotwired/stimulus"
import Chart from "chart.js/auto"

export default class extends Controller {
  connect() {
    const data = JSON.parse(this.element.dataset.chartData)
    this.createChart(data)
  }

  createChart(data) {
    const ctx = this.element.getContext('2d')
    
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: Object.keys(data),
        datasets: [{
          label: 'Daily Signups',
          data: Object.values(data),
          borderColor: '#8B5CF6',
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          borderWidth: 2,
          fill: true
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }
}

// app/javascript/controllers/dashboard_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.startAutoRefresh()
  }

  disconnect() {
    this.stopAutoRefresh()
  }

  startAutoRefresh() {
    this.refreshInterval = setInterval(() => {
      this.refreshStats()
    }, 30000) // Refresh every 30 seconds
  }

  stopAutoRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval)
    }
  }

  refreshStats() {
    fetch(window.location.href, {
      headers: {
        'Accept': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      this.updateStatsDisplay(data)
    })
  }

  updateStatsDisplay(data) {
    // Update stat numbers with new data
    document.querySelectorAll('.stat-number').forEach((element, index) => {
      if (data.stats && data.stats[index]) {
        element.textContent = data.stats[index]
      }
    })
  }
}
```

#### Acceptance Criteria
- [ ] Dashboard displays key metrics clearly
- [ ] Charts visualize trends and performance
- [ ] Real-time updates work without page refresh
- [ ] Analytics page shows detailed project insights
- [ ] Date range filtering affects all visualizations
- [ ] Export functionality works for all data

---

## Phase 4: Public Landing Pages (Week 6)
*Business Value: Professional waitlist pages that convert visitors*

### Task 4.1: Landing Page System
**Business Value**: Beautiful, conversion-optimized public pages  
**Effort**: 5 days  
**Priority**: P0 (Blocker)

#### Routes
```ruby
# config/routes.rb (add public routes)
Rails.application.routes.draw do
  # ... existing routes ...
  
  # Public waitlist pages
  get '/:slug', to: 'public/waitlists#show', as: :public_waitlist
  post '/:slug/join', to: 'public/waitlists#create', as: :join_waitlist
  get '/:slug/confirm/:token', to: 'public/waitlists#confirm', as: :confirm_waitlist
end
```

#### Controllers
```ruby
# app/controllers/public/waitlists_controller.rb
class Public::WaitlistsController < ApplicationController
  skip_before_action :authenticate_user!
  before_action :find_project
  before_action :track_page_view, only: [:show]
  
  layout 'public'
  
  def show
    @custom_fields = @project.custom_fields.active.ordered
  end
  
  def create
    track_signup_started
    
    result = WaitlistSignupService.new(@project, signup_params).call
    
    if result.success?
      track_signup_completed(result.waitlist_entry)
      render :success
    else
      @errors = result.errors
      @custom_fields = @project.custom_fields.active.ordered
      render :show, status: :unprocessable_entity
    end
  end
  
  def confirm
    entry = @project.waitlist_entries.find_by(confirmation_token: params[:token])
    
    if entry&.pending?
      entry.confirm!
      @confirmed_entry = entry
      render :confirmed
    else
      redirect_to public_waitlist_path(@project.slug), 
                  alert: 'Invalid or expired confirmation link.'
    end
  end
  
  private
  
  def find_project
    @project = Project.find_by!(slug: params[:slug], status: :active)
  rescue ActiveRecord::RecordNotFound
    render file: "#{Rails.root}/public/404.html", status: :not_found
  end
  
  def signup_params
    params.permit(:email, :name, :company, :message, custom_fields: {})
          .merge(request_metadata)
  end
  
  def request_metadata
    {
      ip_address: request.remote_ip,
      user_agent: request.user_agent,
      referrer: request.referrer,
      utm_source: params[:utm_source],
      utm_medium: params[:utm_medium],
      utm_campaign: params[:utm_campaign]
    }
  end
  
  def track_page_view
    analytics_service.track_event(
      :page_view,
      request_data: request_metadata.merge(session_id: session.id.to_s)
    )
  end
  
  def track_signup_started
    analytics_service.track_event(
      :signup_started,
      request_data: request_metadata.merge(session_id: session.id.to_s)
    )
  end
  
  def track_signup_completed(waitlist_entry)
    analytics_service.track_event(
      :signup_completed,
      waitlist_entry: waitlist_entry,
      request_data: request_metadata.merge(session_id: session.id.to_s)
    )
  end
  
  def analytics_service
    @analytics_service ||= AnalyticsService.new(@project)
  end
end
```

#### Public Layout
```erb
<!-- app/views/layouts/public.html.erb -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- SEO Meta Tags -->
  <title><%= @project.name %> - Join the Waitlist</title>
  <meta name="description" content="<%= @project.description %>">
  
  <!-- Open Graph Tags -->
  <meta property="og:title" content="<%= @project.name %> - Early Access">
  <meta property="og:description" content="<%= @project.description %>">
  <meta property="og:image" content="<%= @project.logo_url.presence || asset_url('og-default.png') %>">
  <meta property="og:url" content="<%= public_waitlist_url(@project.slug) %>">
  
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  
  <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
  <%= javascript_importmap_tags %>
  
  <!-- Custom Branding -->
  <% if @project.branding_colors.present? %>
    <style>
      :root {
        --primary-color: <%= @project.branding_colors['primary'] || '#8B5CF6' %>;
        --secondary-color: <%= @project.branding_colors['secondary'] || '#EC4899' %>;
      }
    </style>
  <% end %>
</head>

<body class="bg-gradient-to-br from-purple-50 to-blue-50 min-h-screen">
  <%= yield %>
  
  <!-- Analytics Tracking -->
  <script>
    // Track page interactions
    document.addEventListener('DOMContentLoaded', function() {
      // Track scroll depth
      let maxScroll = 0;
      window.addEventListener('scroll', function() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        if (scrollPercent > maxScroll) {
          maxScroll = scrollPercent;
          if (maxScroll >= 25 && maxScroll < 50) {
            trackEvent('scroll_25');
          } else if (maxScroll >= 50 && maxScroll < 75) {
            trackEvent('scroll_50');
          } else if (maxScroll >= 75) {
            trackEvent('scroll_75');
          }
        }
      });
      
      // Track time on page
      const startTime = Date.now();
      window.addEventListener('beforeunload', function() {
        const timeOnPage = Math.round((Date.now() - startTime) / 1000);
        trackEvent('time_on_page', { seconds: timeOnPage });
      });
    });
    
    function trackEvent(eventType, metadata = {}) {
      fetch('/api/v1/analytics_events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          project_slug: '<%= @project.slug %>',
          event_type: eventType,
          metadata: metadata
        })
      });
    }
  </script>
</body>
</html>
```

#### Views
```erb
<!-- app/views/public/waitlists/show.html.erb -->
<div class="container mx-auto px-4 py-16">
  <!-- Header -->
  <div class="text-center mb-12">
    <% if @project.logo_url.present? %>
      <img src="<%= @project.logo_url %>" alt="<%= @project.name %>" class="w-16 h-16 mx-auto mb-6">
    <% end %>
    
    <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
      <%= @project.name %>
    </h1>
    
    <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
      <%= @project.description %>
    </p>
  </div>
  
  <!-- Signup Form -->
  <div class="max-w-md mx-auto">
    <div class="bg-white rounded-2xl shadow-xl p-8">
      <%= form_with url: join_waitlist_path(@project.slug), 
                    method: :post,
                    local: true,
                    class: "space-y-6",
                    data: { controller: "signup-form" } do |form| %>
        
        <div class="text-center mb-6">
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Get Early Access</h3>
          <p class="text-gray-600">
            Join <%= @project.signup_count %> others on the waitlist
          </p>
        </div>
        
        <!-- Email Field -->
        <div>
          <%= form.email_field :email, 
              placeholder: "Enter your email address",
              required: true,
              class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" %>
        </div>
        
        <!-- Name Field -->
        <% if @project.collect_names? %>
          <div>
            <%= form.text_field :name, 
                placeholder: "Your first name",
                class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" %>
          </div>
        <% end %>
        
        <!-- Company Field -->
        <% if @project.collect_companies? %>
          <div>
            <%= form.text_field :company, 
                placeholder: "Company (optional)",
                class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" %>
          </div>
        <% end %>
        
        <!-- Custom Fields -->
        <% @custom_fields.each do |field| %>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <%= field.label %>
              <%= " *" if field.required? %>
            </label>
            
            <% case field.field_type %>
            <% when 'text', 'email', 'phone' %>
              <%= text_field_tag "custom_fields[#{field.id}]", "", 
                  placeholder: field.help_text,
                  required: field.required?,
                  type: field.field_type == 'email' ? 'email' : 'text',
                  class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" %>
            
            <% when 'select' %>
              <%= select_tag "custom_fields[#{field.id}]", 
                  options_for_select([['Select...', '']] + field.options.map { |opt| [opt, opt] }),
                  required: field.required?,
                  class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" %>
            
            <% when 'textarea' %>
              <%= text_area_tag "custom_fields[#{field.id}]", "", 
                  placeholder: field.help_text,
                  required: field.required?,
                  rows: 3,
                  class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" %>
            <% end %>
          </div>
        <% end %>
        
        <!-- Submit Button -->
        <%= form.submit "Join the Waitlist",
            class: "w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-4 px-6 rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg",
            data: { action: "click->signup-form#trackSubmit" } %>
        
        <!-- Trust Signals -->
        <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mt-4">
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
            </svg>
            Secure & Private
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
            No Spam Ever
          </div>
        </div>
      <% end %>
      
      <!-- Error Display -->
      <% if defined?(@errors) && @errors.present? %>
        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl">
          <ul class="text-red-700 text-sm">
            <% @errors.each do |error| %>
              <li><%= error %></li>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>
  </div>
  
  <!-- Social Proof -->
  <div class="text-center mt-12">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-lg mx-auto">
      <div>
        <div class="text-2xl font-bold text-purple-600">
          <%= @project.signup_count %>
        </div>
        <div class="text-gray-600 text-sm">People Waiting</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-purple-600">
          <%= number_to_percentage(@project.conversion_rate, precision: 1) %>
        </div>
        <div class="text-gray-600 text-sm">Conversion Rate</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-purple-600">
          <%= @project.launch_threshold %>
        </div>
        <div class="text-gray-600 text-sm">Launch Goal</div>
      </div>
    </div>
  </div>
</div>
```

#### Stimulus Controller for Form Interactions
```javascript
// app/javascript/controllers/signup_form_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.trackFormView()
  }

  trackFormView() {
    this.trackEvent('form_viewed')
  }

  trackSubmit() {
    this.trackEvent('form_submitted')
  }

  trackEvent(eventType, metadata = {}) {
    fetch('/api/v1/analytics_events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: JSON.stringify({
        project_slug: window.location.pathname.substring(1),
        event_type: eventType,
        metadata: metadata
      })
    })
  }
}
```

#### Acceptance Criteria
- [ ] Public pages load for active projects only
- [ ] Forms capture all required and custom fields
- [ ] Analytics events track all user interactions
- [ ] Email confirmation workflow works end-to-end
- [ ] Pages are mobile-responsive and fast-loading
- [ ] SEO meta tags are properly set
- [ ] Error handling provides clear user feedback

---

## Phase 5: Production Readiness & Advanced Features (Week 7-8)
*Business Value: Enterprise-grade security, performance, and scalability*

### Task 5.1: Security & Performance Enhancements
**Business Value**: Production-ready security and performance  
**Effort**: 4 days  
**Priority**: P0 (Blocker)

#### Security Configuration
```ruby
# Gemfile additions
gem 'rack-attack'
gem 'brakeman'
gem 'bundler-audit'

# config/application.rb
config.middleware.use Rack::Attack
config.force_ssl = true
```

#### Rate Limiting Configuration
```ruby
# config/initializers/rack_attack.rb
class Rack::Attack
  # Throttle all requests by IP (60rpm)
  throttle('req/ip', limit: 300, period: 5.minutes) do |req|
    req.ip
  end

  # Throttle POST requests to /*/join by IP address
  throttle('logins/ip', limit: 5, period: 20.seconds) do |req|
    if req.path.match?(/.*\/join$/) && req.post?
      req.ip
    end
  end

  # Throttle POST requests to /*/join by email param
  throttle('logins/email', limit: 5, period: 20.seconds) do |req|
    if req.path.match?(/.*\/join$/) && req.post?
      req.params['email'].presence
    end
  end
end
```

#### Content Security Policy
```ruby
# config/initializers/content_security_policy.rb
Rails.application.configure do
  config.content_security_policy do |policy|
    policy.default_src :self, :https
    policy.font_src    :self, :https, :data
    policy.img_src     :self, :https, :data
    policy.object_src  :none
    policy.script_src  :self, :https
    policy.style_src   :self, :https, :unsafe_inline
    
    # Specify URI for violation reports
    # policy.report_uri "/csp-violation-report-endpoint"
  end
end
```

### Task 5.2: Rails 8 Solid Trifecta Integration
**Business Value**: Modern Rails 8 architecture with solid foundations  
**Effort**: 3 days  
**Priority**: P1 (High)

#### Solid Configuration
```ruby
# Gemfile
gem 'solid_queue'
gem 'solid_cache'
gem 'solid_cable'

# config/cable.yml
development:
  adapter: solid_cable
  
production:
  adapter: solid_cable

# config/cache.rb
Rails.application.configure do
  config.cache_store = :solid_cache_store
end

# config/queue.yml
production:
  dispatchers:
    - polling_interval: 1
      batch_size: 500
  workers:
    - queues: default
      threads: 3
      processes: 5
```

#### Background Job Enhancement
```ruby
# app/jobs/application_job.rb
class ApplicationJob < ActiveJob::Base
  # Automatically retry jobs that encountered a deadlock
  retry_on ActiveRecord::Deadlocked

  # Most jobs are safe to ignore if the underlying records are no longer available
  discard_on ActiveJob::DeserializationError
  
  # Custom error handling
  rescue_from StandardError do |exception|
    Rails.logger.error "Job failed: #{exception.message}"
    Rails.logger.error exception.backtrace.join("\n")
    
    # Notify monitoring service
    # ErrorTracker.notify(exception)
    
    raise exception
  end
end
```

### Task 5.3: Monitoring & Observability
**Business Value**: Production monitoring and error tracking  
**Effort**: 2 days  
**Priority**: P1 (High)

#### Health Check Endpoints
```ruby
# config/routes.rb
Rails.application.routes.draw do
  # Health check endpoints
  get '/health', to: 'health#index'
  get '/health/detailed', to: 'health#detailed'
  
  # ... existing routes ...
end

# app/controllers/health_controller.rb
class HealthController < ApplicationController
  skip_before_action :authenticate_user!
  
  def index
    render json: { status: 'ok', timestamp: Time.current }
  end
  
  def detailed
    checks = {
      database: database_check,
      cache: cache_check,
      queue: queue_check,
      storage: storage_check
    }
    
    overall_status = checks.values.all? { |check| check[:status] == 'ok' } ? 'ok' : 'error'
    
    render json: {
      status: overall_status,
      timestamp: Time.current,
      checks: checks
    }
  end
  
  private
  
  def database_check
    ActiveRecord::Base.connection.execute('SELECT 1')
    { status: 'ok' }
  rescue => e
    { status: 'error', message: e.message }
  end
  
  def cache_check
    Rails.cache.write('health_check', 'ok', expires_in: 1.minute)
    result = Rails.cache.read('health_check')
    result == 'ok' ? { status: 'ok' } : { status: 'error', message: 'Cache write/read failed' }
  rescue => e
    { status: 'error', message: e.message }
  end
  
  def queue_check
    # Check if SolidQueue is running
    SolidQueue::Job.count
    { status: 'ok' }
  rescue => e
    { status: 'error', message: e.message }
  end
  
  def storage_check
    # Basic storage check
    { status: 'ok' }
  rescue => e
    { status: 'error', message: e.message }
  end
end
```

#### Logging Enhancement
```ruby
# config/environments/production.rb
Rails.application.configure do
  # Use structured logging
  config.log_formatter = proc do |severity, datetime, progname, msg|
    {
      timestamp: datetime.iso8601,
      level: severity,
      message: msg,
      process_id: Process.pid,
      thread_id: Thread.current.object_id
    }.to_json + "\n"
  end
  
  # Log to stdout for containerized deployments
  config.logger = ActiveSupport::Logger.new(STDOUT)
  config.log_level = :info
end
```

#### Acceptance Criteria
- [ ] Rate limiting prevents abuse
- [ ] Content Security Policy blocks XSS
- [ ] SolidQueue processes background jobs
- [ ] SolidCache improves performance
- [ ] Health checks work reliably
- [ ] Structured logging provides insights

---

## Implementation Guidelines

### Code Quality Standards
- **Test Coverage**: Minimum 90% for all new code
- **Performance**: Page load times <2 seconds
- **Security**: All inputs validated and sanitized
- **Accessibility**: WCAG 2.1 AA compliance

### Development Workflow
1. **Feature Branch**: Create branch from main
2. **TDD Approach**: Write failing tests first
3. **Implementation**: Build feature to pass tests
4. **Code Review**: Peer review before merge
5. **Testing**: Automated and manual QA
6. **Deployment**: Staged rollout with monitoring

### Database Considerations
- **Indexing**: All foreign keys and query columns indexed
- **Migrations**: Reversible and safe for production
- **Constraints**: Database-level validations where appropriate
- **Performance**: Query optimization and N+1 prevention

### Rails 8 Specific Features
- **Solid Trifecta**: Use SolidQueue, SolidCache, SolidCable instead of Redis
- **Modern JavaScript**: Leverage importmaps and Stimulus
- **Hotwire**: Use Turbo for single-page application feel
- **Security**: Built-in CSRF and XSS protection

### Monitoring & Observability
- **Error Tracking**: Comprehensive error logging
- **Performance Monitoring**: Response time tracking
- **Business Metrics**: Key performance indicators
- **User Analytics**: Behavior and conversion tracking

---

This implementation guide provides a clear path from PRD to production-ready Rails 8 application, with each task designed to deliver immediate business value while building toward the complete WaitlistBuilder platform using modern Rails architecture and the Solid Trifecta.