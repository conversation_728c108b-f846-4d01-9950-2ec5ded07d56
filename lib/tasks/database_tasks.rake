# Rake tasks for managing Rails 8 Solid Trifecta databases

namespace :solid do
  desc "Set up all Solid Trifecta databases"
  task setup: :environment do
    puts "🚀 Setting up Rails 8 Solid Trifecta databases..."
    
    Rake::Task['solid:db:create'].invoke
    Rake::Task['solid:db:migrate'].invoke
    Rake::Task['solid:db:seed'].invoke if Rails.env.development?
    
    puts "✅ Solid Trifecta setup complete!"
  end

  namespace :db do
    desc "Create all Solid Trifecta databases"
    task create: :environment do
      puts "📊 Creating Solid Trifecta databases..."
      
      # Create primary database
      Rake::Task['db:create:primary'].invoke
      
      # Create Solid Queue database
      ActiveRecord::Tasks::DatabaseTasks.create(
        ActiveRecord::Base.configurations.configs_for(env_name: Rails.env, name: "queue").database_configuration
      )
      puts "  ✓ Created Solid Queue database"
      
      # Create Solid Cache database
      ActiveRecord::Tasks::DatabaseTasks.create(
        ActiveRecord::Base.configurations.configs_for(env_name: Rails.env, name: "cache").database_configuration
      )
      puts "  ✓ Created Solid Cache database"
      
      # Create Solid Cable database  
      ActiveRecord::Tasks::DatabaseTasks.create(
        ActiveRecord::Base.configurations.configs_for(env_name: Rails.env, name: "cable").database_configuration
      )
      puts "  ✓ Created Solid Cable database"
      
      # Create specialized cache databases for production
      if Rails.env.production? || Rails.env.staging?
        ActiveRecord::Tasks::DatabaseTasks.create(
          ActiveRecord::Base.configurations.configs_for(env_name: Rails.env, name: "cache_analytics").database_configuration
        )
        puts "  ✓ Created Analytics Cache database"
        
        ActiveRecord::Tasks::DatabaseTasks.create(
          ActiveRecord::Base.configurations.configs_for(env_name: Rails.env, name: "cache_sessions").database_configuration
        )
        puts "  ✓ Created Sessions Cache database"
      end
      
      puts "📊 All Solid Trifecta databases created successfully!"
    end

    desc "Drop all Solid Trifecta databases"
    task drop: :environment do
      puts "🗑️  Dropping Solid Trifecta databases..."
      
      # Drop primary database
      Rake::Task['db:drop:primary'].invoke
      
      # Drop Solid databases
      %w[queue cache cable cache_analytics cache_sessions].each do |db_name|
        begin
          config = ActiveRecord::Base.configurations.configs_for(env_name: Rails.env, name: db_name)
          next unless config
          
          ActiveRecord::Tasks::DatabaseTasks.drop(config.database_configuration)
          puts "  ✓ Dropped #{db_name} database"
        rescue => e
          puts "  ⚠️  Could not drop #{db_name} database: #{e.message}"
        end
      end
      
      puts "🗑️  Solid Trifecta databases dropped!"
    end

    desc "Migrate all Solid Trifecta databases"
    task migrate: :environment do
      puts "🔄 Migrating Solid Trifecta databases..."
      
      # Migrate primary database
      Rake::Task['db:migrate:primary'].invoke
      
      # Install and migrate Solid Queue
      Rake::Task['solid_queue:install:migrations'].invoke
      ActiveRecord::MigrationContext.new(
        Rails.root.join('db/queue_migrate'),
        ActiveRecord::SchemaMigration
      ).migrate
      puts "  ✓ Migrated Solid Queue database"
      
      # Install and migrate Solid Cache
      Rake::Task['solid_cache:install:migrations'].invoke
      ActiveRecord::MigrationContext.new(
        Rails.root.join('db/cache_migrate'),
        ActiveRecord::SchemaMigration
      ).migrate
      puts "  ✓ Migrated Solid Cache database"
      
      # Install and migrate Solid Cable
      Rake::Task['solid_cable:install:migrations'].invoke
      ActiveRecord::MigrationContext.new(
        Rails.root.join('db/cable_migrate'),
        ActiveRecord::SchemaMigration
      ).migrate
      puts "  ✓ Migrated Solid Cable database"
      
      puts "🔄 All Solid Trifecta databases migrated successfully!"
    end

    desc "Reset all Solid Trifecta databases"
    task reset: :environment do
      puts "🔄 Resetting Solid Trifecta databases..."
      
      Rake::Task['solid:db:drop'].invoke
      Rake::Task['solid:db:create'].invoke
      Rake::Task['solid:db:migrate'].invoke
      Rake::Task['solid:db:seed'].invoke if Rails.env.development?
      
      puts "🔄 Solid Trifecta databases reset complete!"
    end

    desc "Seed Solid Trifecta databases with sample data"
    task seed: :environment do
      next unless Rails.env.development?
      
      puts "🌱 Seeding Solid Trifecta databases..."
      
      # Run primary database seeds
      Rake::Task['db:seed'].invoke
      
      # Add some sample jobs to the queue for testing
      10.times do |i|
        WaitlistBuilder::Jobs::ProcessAnalyticsEventJob.perform_later({
          event_type: 'page_view',
          project_slug: 'sample-project',
          session_id: SecureRandom.uuid,
          metadata: { page_url: 'https://sample-project.waitlistbuilder.com' }
        })
      end
      
      puts "  ✓ Added sample jobs to Solid Queue"
      
      # Warm up cache with sample data
      if defined?(Project)
        Project.limit(5).each do |project|
          WaitlistBuilder::CacheWarmer.warm_project_cache(project)
        end
        puts "  ✓ Warmed Solid Cache with sample data"
      end
      
      puts "🌱 Solid Trifecta seeding complete!"
    end
  end

  namespace :queue do
    desc "Start Solid Queue worker"
    task start: :environment do
      puts "🏃 Starting Solid Queue worker..."
      exec "bundle exec solid_queue"
    end

    desc "Stop Solid Queue worker"
    task stop: :environment do
      puts "🛑 Stopping Solid Queue worker..."
      pid_file = Rails.root.join('tmp', 'pids', 'solid_queue_supervisor.pid')
      
      if File.exist?(pid_file)
        pid = File.read(pid_file).to_i
        Process.kill('TERM', pid)
        File.delete(pid_file)
        puts "  ✓ Solid Queue worker stopped (PID: #{pid})"
      else
        puts "  ⚠️  No PID file found. Worker may not be running."
      end
    end

    desc "Restart Solid Queue worker"
    task restart: :environment do
      Rake::Task['solid:queue:stop'].invoke
      sleep 2
      Rake::Task['solid:queue:start'].invoke
    end

    desc "Clear all jobs from Solid Queue"
    task clear: :environment do
      puts "🧹 Clearing all jobs from Solid Queue..."
      
      ActiveRecord::Base.connected_to(role: :writing, shard: :queue) do
        SolidQueue::Job.delete_all
        SolidQueue::ScheduledExecution.delete_all
        SolidQueue::FailedExecution.delete_all
        SolidQueue::BlockedExecution.delete_all
      end
      
      puts "  ✓ All jobs cleared from Solid Queue"
    end

    desc "Show Solid Queue status"
    task status: :environment do
      puts "📊 Solid Queue Status:"
      
      ActiveRecord::Base.connected_to(role: :reading, shard: :queue) do
        queued = SolidQueue::Job.count
        scheduled = SolidQueue::ScheduledExecution.count
        failed = SolidQueue::FailedExecution.count
        blocked = SolidQueue::BlockedExecution.count
        
        puts "  📋 Queued jobs: #{queued}"
        puts "  ⏰ Scheduled jobs: #{scheduled}"
        puts "  ❌ Failed jobs: #{failed}"
        puts "  🚫 Blocked jobs: #{blocked}"
        puts "  📊 Total jobs: #{queued + scheduled + failed + blocked}"
      end
    end
  end

  namespace :cache do
    desc "Clear all Solid Cache data"
    task clear: :environment do
      puts "🧹 Clearing Solid Cache..."
      
      Rails.cache.clear
      
      puts "  ✓ Solid Cache cleared"
    end

    desc "Show Solid Cache statistics"
    task stats: :environment do
      puts "📊 Solid Cache Statistics:"
      
      ActiveRecord::Base.connected_to(role: :reading, shard: :cache) do
        total_entries = SolidCache::Entry.count
        total_size = SolidCache::Entry.sum(:byte_size)
        oldest_entry = SolidCache::Entry.minimum(:created_at)
        newest_entry = SolidCache::Entry.maximum(:created_at)
        
        puts "  📝 Total entries: #{total_entries}"
        puts "  💾 Total size: #{ActiveSupport::NumberHelper.number_to_human_size(total_size)}"
        puts "  📅 Oldest entry: #{oldest_entry&.strftime('%Y-%m-%d %H:%M:%S')}"
        puts "  📅 Newest entry: #{newest_entry&.strftime('%Y-%m-%d %H:%M:%S')}"
      end
    end

    desc "Warm up cache with frequently accessed data"
    task warm: :environment do
      puts "🔥 Warming up Solid Cache..."
      
      # Warm up project caches
      Project.active.includes(:waitlist_entries, :custom_fields).find_each do |project|
        WaitlistBuilder::CacheWarmer.warm_project_cache(project)
      end
      
      # Warm up user caches
      User.joins(:projects).distinct.limit(100).find_each do |user|
        WaitlistBuilder::CacheWarmer.warm_user_cache(user)
      end
      
      puts "  ✓ Cache warming complete"
    end
  end

  namespace :cable do
    desc "Show active WebSocket connections"
    task connections: :environment do
      puts "🔌 Active WebSocket Connections:"
      
      connection_count = ActionCable.server.connections.count
      puts "  📊 Total connections: #{connection_count}"
      
      # Group connections by channel
      channels = {}
      ActionCable.server.connections.each do |connection|
        connection.subscriptions.subscriptions.each do |subscription|
          channel_name = subscription.identifier['channel']
          channels[channel_name] ||= 0
          channels[channel_name] += 1
        end
      end
      
      channels.each do |channel, count|
        puts "  📺 #{channel}: #{count} subscription(s)"
      end
    end

    desc "Clear Solid Cable message history"
    task clear: :environment do
      puts "🧹 Clearing Solid Cable messages..."
      
      ActiveRecord::Base.connected_to(role: :writing, shard: :cable) do
        SolidCable::Message.delete_all
      end
      
      puts "  ✓ Solid Cable messages cleared"
    end
  end

  desc "Show comprehensive Solid Trifecta status"
  task status: :environment do
    puts "🚀 Rails 8 Solid Trifecta Status Report"
    puts "=" * 50
    
    Rake::Task['solid:queue:status'].invoke
    puts ""
    Rake::Task['solid:cache:stats'].invoke
    puts ""
    Rake::Task['solid:cable:connections'].invoke
    
    puts ""
    puts "📊 System Health: All Solid Trifecta components operational"
  end

  desc "Health check all Solid Trifecta components"
  task health: :environment do
    puts "🏥 Solid Trifecta Health Check"
    puts "=" * 30
    
    errors = []
    
    # Test database connections
    begin
      ActiveRecord::Base.connected_to(role: :reading, shard: :queue) { SolidQueue::Job.count }
      puts "  ✅ Solid Queue database: Connected"
    rescue => e
      puts "  ❌ Solid Queue database: #{e.message}"
      errors << "Solid Queue database connection failed"
    end
    
    begin
      ActiveRecord::Base.connected_to(role: :reading, shard: :cache) { SolidCache::Entry.count }
      puts "  ✅ Solid Cache database: Connected"
    rescue => e
      puts "  ❌ Solid Cache database: #{e.message}"
      errors << "Solid Cache database connection failed"
    end
    
    begin
      ActiveRecord::Base.connected_to(role: :reading, shard: :cable) { SolidCable::Message.count }
      puts "  ✅ Solid Cable database: Connected"
    rescue => e
      puts "  ❌ Solid Cable database: #{e.message}"
      errors << "Solid Cable database connection failed"
    end
    
    # Test cache operations
    begin
      test_key = "health_check_#{SecureRandom.hex(8)}"
      Rails.cache.write(test_key, "test_value", expires_in: 1.minute)
      result = Rails.cache.read(test_key)
      
      if result == "test_value"
        puts "  ✅ Cache operations: Working"
        Rails.cache.delete(test_key)
      else
        puts "  ❌ Cache operations: Failed to read written value"
        errors << "Cache read/write operations failed"
      end
    rescue => e
      puts "  ❌ Cache operations: #{e.message}"
      errors << "Cache operations failed"
    end
    
    # Test job queueing
    begin
      define_test_health_check_job
      job = TestHealthCheckJob.perform_later
      puts "  ✅ Job queueing: Working (Job ID: #{job.job_id})"
    rescue => e
      puts "  ❌ Job queueing: #{e.message}"
      errors << "Job queueing failed"
    end
    
    puts ""
    if errors.empty?
      puts "🎉 All Solid Trifecta components are healthy!"
      exit 0
    else
      puts "⚠️  Health check found #{errors.count} issue(s):"
      errors.each { |error| puts "    - #{error}" }
      exit 1
    end
  end
end

# Simple test job for health checks
# This class will be defined when needed during health checks
def define_test_health_check_job
  return if defined?(TestHealthCheckJob)
  
  Object.const_set('TestHealthCheckJob', Class.new(ApplicationJob) do
    queue_as :default
    
    def perform
      Rails.logger.info "Health check job executed successfully"
    end
  end)
end