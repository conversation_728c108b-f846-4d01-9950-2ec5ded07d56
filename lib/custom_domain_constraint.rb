class CustomDomainConstraint
  def matches?(request)
    # Check if the request is from a custom domain
    # Exclude our main application domains
    excluded_domains = [
      'localhost',
      'waitlistbuilder.com',
      'www.waitlistbuilder.com'
    ]
    
    host = request.host
    return false if excluded_domains.include?(host)
    
    # Check if this domain exists in our landing pages
    LandingPage.published.exists?(custom_domain: host)
  rescue
    false
  end
end