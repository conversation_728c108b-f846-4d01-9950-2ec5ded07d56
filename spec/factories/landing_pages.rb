FactoryBot.define do
  factory :landing_page do
    project { nil }
    title { "MyString" }
    subtitle { "MyString" }
    description { "MyText" }
    hero_image_url { "MyString" }
    primary_color { "MyString" }
    secondary_color { "MyString" }
    custom_css { "MyText" }
    custom_domain { "MyString" }
    published { false }
    slug { "MyString" }
    meta_title { "MyString" }
    meta_description { "MyText" }
    meta_keywords { "MyString" }
    og_title { "MyString" }
    og_description { "MyText" }
    og_image_url { "MyString" }
    twitter_title { "MyString" }
    twitter_description { "MyText" }
    twitter_image_url { "MyString" }
    canonical_url { "MyString" }
    schema_markup { "MyText" }
    robots_meta { "MyString" }
  end
end
