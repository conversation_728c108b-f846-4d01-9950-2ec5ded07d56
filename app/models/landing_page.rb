class LandingPage < ApplicationRecord
  acts_as_tenant :account, through: :project
  
  belongs_to :project
  has_many :waitlist_entries, dependent: :destroy
  
  # FriendlyId for SEO-friendly URLs
  extend FriendlyId
  friendly_id :title, use: [:slugged, :scoped], scope: :project
  
  # Validations
  validates :title, presence: true, length: { maximum: 100 }
  validates :subtitle, length: { maximum: 200 }, allow_blank: true
  validates :description, length: { maximum: 1000 }, allow_blank: true
  validates :slug, presence: true, uniqueness: { scope: :project_id }
  validates :custom_domain, uniqueness: true, allow_blank: true,
            format: { with: /\A[a-zA-Z0-9][a-zA-Z0-9\-\.]{1,253}[a-zA-Z0-9]\.[a-zA-Z]{2,}\z/ }
  
  # SEO Validations
  validates :meta_title, length: { maximum: 60 }, allow_blank: true
  validates :meta_description, length: { maximum: 160 }, allow_blank: true
  validates :og_title, length: { maximum: 60 }, allow_blank: true
  validates :og_description, length: { maximum: 160 }, allow_blank: true
  validates :twitter_title, length: { maximum: 60 }, allow_blank: true
  validates :twitter_description, length: { maximum: 160 }, allow_blank: true
  
  # Color validations
  validates :primary_color, format: { with: /\A#[0-9A-Fa-f]{6}\z/ }
  validates :secondary_color, format: { with: /\A#[0-9A-Fa-f]{6}\z/ }
  
  # Scopes
  scope :published, -> { where(published: true) }
  scope :draft, -> { where(published: false) }
  scope :popular, -> { order(page_views: :desc) }
  scope :recent, -> { order(created_at: :desc) }
  
  # Callbacks
  before_validation :generate_slug_if_blank
  before_validation :set_seo_defaults
  before_validation :set_account_from_project
  after_update :update_search_engines, if: :saved_change_to_published?
  
  # SEO Methods
  def seo_title
    meta_title.presence || title
  end
  
  def seo_description
    meta_description.presence || description&.truncate(160) || "Join the waitlist for #{title}"
  end
  
  def seo_keywords
    meta_keywords.presence || generate_keywords
  end
  
  def og_title_or_default
    og_title.presence || seo_title
  end
  
  def og_description_or_default
    og_description.presence || seo_description
  end
  
  def twitter_title_or_default
    twitter_title.presence || seo_title
  end
  
  def twitter_description_or_default
    twitter_description.presence || seo_description
  end
  
  def full_url
    if custom_domain.present?
      "https://#{custom_domain}"
    else
      Rails.application.routes.url_helpers.public_landing_page_url(self, host: default_host)
    end
  end
  
  def canonical_url_or_default
    canonical_url.presence || full_url
  end
  
  # Schema.org structured data
  def default_schema_markup
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": seo_title,
      "description": seo_description,
      "url": full_url,
      "mainEntity": {
        "@type": "Product",
        "name": title,
        "description": description || seo_description,
        "offers": {
          "@type": "Offer",
          "availability": "https://schema.org/PreOrder",
          "price": "0",
          "priceCurrency": "USD"
        }
      },
      "publisher": {
        "@type": "Organization",
        "name": project.account.name,
        "url": full_url
      }
    }.to_json
  end
  
  def schema_markup_or_default
    schema_markup.presence || default_schema_markup
  end
  
  # Analytics
  def increment_page_view!
    increment!(:page_views)
  end
  
  def conversion_rate
    return 0 if page_views == 0
    (waitlist_entries.count.to_f / page_views.to_f * 100).round(2)
  end
  
  # Status helpers
  def draft?
    !published?
  end
  
  def live?
    published? && (custom_domain.present? || slug.present?)
  end
  
  # Domain helpers
  def using_custom_domain?
    custom_domain.present?
  end
  
  def preview_url
    if using_custom_domain?
      "https://#{custom_domain}"
    else
      Rails.application.routes.url_helpers.preview_landing_page_url(project, self, host: default_host)
    end
  end
  
  # Design helpers
  def primary_color_rgb
    hex_to_rgb(primary_color)
  end
  
  def secondary_color_rgb
    hex_to_rgb(secondary_color)
  end
  
  # Generate sitemap entry
  def to_sitemap_xml
    <<~XML
      <url>
        <loc>#{full_url}</loc>
        <lastmod>#{updated_at.strftime('%Y-%m-%d')}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
      </url>
    XML
  end
  
  private
  
  def generate_slug_if_blank
    return if slug.present?
    
    base_slug = title.to_s.parameterize
    counter = 1
    potential_slug = base_slug
    
    while project.landing_pages.where(slug: potential_slug).where.not(id: id).exists?
      potential_slug = "#{base_slug}-#{counter}"
      counter += 1
    end
    
    self.slug = potential_slug
  end
  
  def set_seo_defaults
    self.meta_title ||= title if title.present?
    self.meta_description ||= description&.truncate(160) if description.present?
    self.og_title ||= meta_title if meta_title.present?
    self.og_description ||= meta_description if meta_description.present?
    self.twitter_title ||= meta_title if meta_title.present?
    self.twitter_description ||= meta_description if meta_description.present?
  end
  
  def generate_keywords
    keywords = []
    keywords << title.downcase if title.present?
    keywords << project.name.downcase if project.name.present?
    keywords += %w[waitlist signup early access beta launch product]
    keywords.uniq.join(', ')
  end
  
  def hex_to_rgb(hex)
    hex.match(/^#(..)(..)(..)$/).captures.map(&:hex)
  end
  
  def update_search_engines
    return unless published?
    
    # Update last crawled timestamp
    update_column(:last_crawled_at, Time.current)
    
    # In a real app, you might:
    # - Submit to Google Search Console API
    # - Ping search engines
    # - Update sitemaps
    # - Clear CDN cache
  end
  
  def should_generate_new_friendly_id?
    title_changed? || slug.blank?
  end
  
  def set_account_from_project
    self.account = project.account if project && !account
  end
  
  def default_host
    Rails.env.production? ? 'waitlistbuilder.com' : 'localhost:3000'
  end
end