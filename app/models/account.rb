class Account < ApplicationRecord
  # Associations
  belongs_to :owner, class_name: 'User'
  has_many :account_users, dependent: :destroy
  has_many :users, through: :account_users
  has_many :projects, dependent: :destroy
  has_many :landing_pages, through: :projects
  has_many :waitlist_entries, through: :projects
  
  # Validations
  validates :name, presence: true, length: { maximum: 100 }
  validates :subdomain, presence: true, uniqueness: { case_sensitive: false },
            format: { with: /\A[a-z0-9\-]+\z/, message: "can only contain lowercase letters, numbers, and hyphens" },
            length: { minimum: 2, maximum: 30 }
  validates :status, inclusion: { in: %w[active inactive suspended] }
  validates :plan, inclusion: { in: %w[free starter pro enterprise] }
  validates :time_zone, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name) }
  validates :projects_limit, presence: true, numericality: { greater_than: 0 }
  
  # Scopes
  scope :active, -> { where(active: true, status: 'active') }
  scope :by_plan, ->(plan) { where(plan: plan) }
  scope :with_projects, -> { joins(:projects).distinct }
  
  # Callbacks
  before_validation :normalize_subdomain
  after_create :create_owner_membership
  
  # Instance methods
  def display_name
    name.presence || "Account ##{id}"
  end
  
  def can_create_project?
    projects.count < projects_limit
  end
  
  def usage_percentage
    return 0 if projects_limit.zero?
    (projects.count.to_f / projects_limit * 100).round(1)
  end
  
  def total_waitlist_entries
    ActsAsTenant.without_tenant do
      waitlist_entries.count
    end
  end
  
  def total_confirmed_entries
    ActsAsTenant.without_tenant do
      waitlist_entries.confirmed.count
    end
  end
  
  def upgrade_required?
    plan == 'free' && (projects.count >= projects_limit || total_waitlist_entries > 100)
  end
  
  def billing_email_or_owner
    billing_email.present? ? billing_email : owner.email
  end
  
  def to_param
    subdomain
  end
  
  private
  
  def normalize_subdomain
    return unless subdomain.present?
    self.subdomain = subdomain.to_s.downcase.strip.gsub(/[^a-z0-9\-]/, '-').squeeze('-').chomp('-')
  end
  
  def create_owner_membership
    account_users.create!(
      user: owner,
      role: 'owner',
      joined_at: Time.current,
      permissions: { all: true }
    )
  end
end
