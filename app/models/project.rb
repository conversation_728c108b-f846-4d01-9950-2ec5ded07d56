class Project < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged
  
  # Multi-tenancy
  acts_as_tenant :account
  
  # Associations
  belongs_to :account
  has_many :waitlist_entries, dependent: :destroy
  has_many :landing_pages, dependent: :destroy
  
  # Validations
  validates :name, presence: true, length: { maximum: 100 }
  validates :slug, presence: true, uniqueness: true
  validates :status, inclusion: { in: %w[draft active paused archived] }
  validates :description, length: { maximum: 1000 }
  validates :website_url, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]), allow_blank: true }
  validates :custom_domain, format: { with: /\A[a-z0-9\-\.]+\z/i, allow_blank: true }
  
  # Scopes
  scope :active, -> { where(status: 'active') }
  scope :launched, -> { where.not(launched_at: nil) }
  scope :draft, -> { where(status: 'draft') }
  scope :with_entries, -> { joins(:waitlist_entries).distinct }
  scope :by_status, ->(status) { where(status: status) }
  scope :recent, -> { order(created_at: :desc) }
  
  # Callbacks
  before_validation :generate_slug, on: :create
  after_update :track_launch, if: :saved_change_to_status?
  
  # Instance methods
  def display_name
    name.presence || "Project ##{id}"
  end
  
  def launched?
    launched_at.present?
  end
  
  def can_launch?
    status == 'draft' && name.present? && description.present?
  end
  
  def launch!
    return false unless can_launch?
    update!(status: 'active', launched_at: Time.current)
  end
  
  def total_signups
    waitlist_entries.count
  end
  
  def confirmed_signups
    waitlist_entries.where(status: 'confirmed').count
  end
  
  def pending_signups
    waitlist_entries.where(status: 'pending').count
  end
  
  def conversion_rate
    return 0 if total_signups.zero?
    (confirmed_signups.to_f / total_signups * 100).round(2)
  end
  
  def recent_signups(limit = 10)
    waitlist_entries.order(created_at: :desc).limit(limit)
  end
  
  def daily_signups(days = 7)
    waitlist_entries
      .where(created_at: days.days.ago..Time.current)
      .group_by_day(:created_at, range: days.days.ago..Time.current)
      .count
  end
  
  def referral_stats
    {
      total_referrals: waitlist_entries.where.not(referred_by: nil).count,
      top_referrers: waitlist_entries.group(:referred_by).count.sort_by(&:last).reverse.first(10)
    }
  end
  
  def public_url
    if custom_domain.present?
      "https://#{custom_domain}"
    else
      "https://#{account.subdomain}.waitlistbuilder.com/#{slug}"
    end
  end
  
  def admin_url
    "https://#{account.subdomain}.waitlistbuilder.com/projects/#{slug}"
  end
  
  def should_generate_new_friendly_id?
    name_changed? || super
  end
  
  def branding_colors
    branding.dig('colors') || {
      'primary' => '#3B82F6',
      'secondary' => '#10B981',
      'background' => '#FFFFFF',
      'text' => '#111827'
    }
  end
  
  def social_links
    branding.dig('social') || {}
  end
  
  def meta_tags
    branding.dig('meta') || {
      'title' => name,
      'description' => description,
      'image' => logo_url
    }
  end
  
  def calculate_conversion_rate
    conversion_rate
  end
  
  private
  
  def generate_slug
    return if slug.present?
    self.slug = name.to_s.parameterize
    ensure_unique_slug
  end
  
  def ensure_unique_slug
    original_slug = slug
    counter = 1
    
    while Project.exists?(slug: slug)
      self.slug = "#{original_slug}-#{counter}"
      counter += 1
    end
  end
  
  def track_launch
    return unless status == 'active' && launched_at.nil?
    update_column(:launched_at, Time.current)
  end
end
