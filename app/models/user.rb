class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :confirmable, :lockable, :trackable, :timeoutable

  # Associations
  has_many :account_users, dependent: :destroy
  has_many :accounts, through: :account_users
  has_many :owned_accounts, class_name: 'Account', foreign_key: 'owner_id', dependent: :restrict_with_error

  # Validations
  validates :first_name, presence: true, length: { maximum: 50 }
  validates :last_name, presence: true, length: { maximum: 50 }
  validates :email, presence: true, uniqueness: { case_sensitive: false }
  validates :role, inclusion: { in: %w[user admin superadmin] }
  validates :time_zone, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name) }

  # Scopes
  scope :admins, -> { where(admin: true) }
  scope :active, -> { where(locked_at: nil) }
  scope :confirmed, -> { where.not(confirmed_at: nil) }

  # Callbacks
  before_validation :normalize_email
  after_create :create_default_account

  # Instance methods
  def full_name
    "#{first_name} #{last_name}".strip
  end

  def display_name
    full_name.presence || email
  end

  def active_for_authentication?
    super && !locked_at?
  end

  def inactive_message
    locked_at? ? :locked : super
  end

  def timeout_in
    return 1.year if admin?
    30.minutes
  end

  private

  def normalize_email
    self.email = email.to_s.downcase.strip
  end

  def create_default_account
    return if accounts.any?
    
    account = owned_accounts.create!(
      name: "#{full_name}'s Account",
      subdomain: generate_subdomain
    )
    
    # The Account model will create the owner membership via its own callback
    # So we don't need to create it here
  end

  def generate_subdomain
    base = full_name.parameterize.presence || email.split('@').first.parameterize
    subdomain = base
    counter = 1
    
    while Account.exists?(subdomain: subdomain)
      subdomain = "#{base}-#{counter}"
      counter += 1
    end
    
    subdomain
  end
end
