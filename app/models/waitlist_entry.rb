class WaitlistEntry < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant :account
  
  # Associations
  belongs_to :project
  belongs_to :account
  belongs_to :landing_page
  
  # Validations
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :email, uniqueness: { scope: :landing_page_id, message: "is already on the waitlist for this landing page" }
  validates :status, inclusion: { in: %w[pending confirmed rejected spam] }
  validates :referral_code, uniqueness: { allow_blank: true }
  validates :name, length: { maximum: 100 }
  validates :phone_number, length: { maximum: 20 }
  
  # Scopes
  scope :confirmed, -> { where(status: 'confirmed') }
  scope :pending, -> { where(status: 'pending') }
  scope :recent, ->(days = 7) { where(created_at: days.days.ago..Time.current) }
  scope :by_status, ->(status) { where(status: status) }
  scope :with_referrals, -> { where.not(referred_by: nil) }
  scope :verified, -> { where(email_verified: true) }
  scope :unverified, -> { where(email_verified: false) }
  scope :ordered_by_position, -> { order(:position) }
  
  # Callbacks
  before_validation :set_account_and_project_from_landing_page
  before_create :generate_referral_code
  before_create :set_position
  before_create :generate_confirmation_token
  after_create :send_confirmation_email, if: :should_send_confirmation?
  after_create :increment_referrer_count
  after_update :track_confirmation, if: :saved_change_to_status?
  
  # Instance methods
  def confirmed?
    status == 'confirmed'
  end
  
  def pending?
    status == 'pending'
  end
  
  def display_name
    name.presence || email.split('@').first
  end
  
  def confirm!
    return false if confirmed?
    
    transaction do
      update!(
        status: 'confirmed',
        confirmed_at: Time.current,
        email_verified: true
      )
      recalculate_positions
    end
  end
  
  def confirm_email!
    confirm!
  end
  
  def unsubscribe!
    update!(status: 'rejected', unsubscribed_at: Time.current)
  end
  
  def reject!(reason = nil)
    update!(
      status: 'rejected',
      custom_data: custom_data.merge(rejection_reason: reason)
    )
  end
  
  def queue_position
    return position if position.present?
    
    ActsAsTenant.without_tenant do
      project.waitlist_entries
             .confirmed
             .where('created_at <= ?', created_at)
             .count
    end
  end
  
  def estimated_wait_time
    return nil unless confirmed?
    return "You're next!" if position == 1
    return "Coming soon!" if position <= 10
    
    weeks = (position / 10.0).ceil
    "#{weeks} week#{'s' if weeks > 1}"
  end
  
  def referral_url
    return nil unless referral_code.present?
    "#{project.public_url}?ref=#{referral_code}"
  end
  
  def referrals
    ActsAsTenant.without_tenant do
      project.waitlist_entries.where(referred_by: referral_code)
    end
  end
  
  def referrals_count
    ActsAsTenant.without_tenant { referrals.count }
  end
  
  def bonus_positions
    # Move up 1 position for every 3 referrals
    (referrals_count / 3).floor
  end
  
  def effective_position
    [position - bonus_positions, 1].max
  end
  
  def days_waiting
    return 0 unless created_at
    (Time.current - created_at) / 1.day
  end
  
  def source_display
    case source
    when 'direct' then 'Direct'
    when 'social' then 'Social Media'
    when 'referral' then "Referred by #{referred_by}"
    when 'email' then 'Email Campaign'
    when 'ad' then 'Advertisement'
    else 'Unknown'
    end
  end
  
  def to_param
    confirmation_token
  end
  
  private
  
  def set_account_and_project_from_landing_page
    if landing_page
      self.project = landing_page.project if !project
      self.account = landing_page.account if !account
    end
  end
  
  def generate_referral_code
    return if referral_code.present?
    
    loop do
      self.referral_code = SecureRandom.hex(4).upcase
      break unless WaitlistEntry.exists?(referral_code: referral_code)
    end
  end
  
  def set_position
    # Use ActsAsTenant.without_tenant to get accurate count for position
    self.position = ActsAsTenant.without_tenant { project.waitlist_entries.count + 1 }
  end
  
  def generate_confirmation_token
    return if confirmation_token.present?
    
    loop do
      self.confirmation_token = SecureRandom.urlsafe_base64(32)
      break unless WaitlistEntry.exists?(confirmation_token: confirmation_token)
    end
  end
  
  def should_send_confirmation?
    project.double_opt_in? && pending?
  end
  
  def send_confirmation_email
    # Will be implemented when we add ActionMailer
    # WaitlistMailer.confirmation_email(self).deliver_later
    self.confirmation_sent_at = Time.current
    save!
  end
  
  def increment_referrer_count
    return unless referred_by.present?
    
    ActsAsTenant.without_tenant do
      referrer = project.waitlist_entries.find_by(referral_code: referred_by)
      referrer&.increment!(:referrals_count)
    end
  end
  
  def track_confirmation
    return unless status == 'confirmed' && confirmed_at.nil?
    update_column(:confirmed_at, Time.current)
  end
  
  def recalculate_positions
    # Recalculate positions for all confirmed entries
    ActsAsTenant.without_tenant do
      project.waitlist_entries.confirmed.order(:created_at).each_with_index do |entry, index|
        entry.update_column(:position, index + 1)
      end
    end
  end
end
