class AccountUser < ApplicationRecord
  # Associations
  belongs_to :account
  belongs_to :user
  
  # Validations
  validates :role, inclusion: { in: %w[owner admin member viewer] }
  validates :joined_at, presence: true
  validates :user_id, uniqueness: { scope: :account_id, message: "is already a member of this account" }
  
  # Scopes
  scope :active, -> { where(active: true) }
  scope :by_role, ->(role) { where(role: role) }
  scope :owners, -> { where(role: 'owner') }
  scope :admins, -> { where(role: 'admin') }
  scope :recent, -> { order(joined_at: :desc) }
  
  # Callbacks
  before_validation :set_joined_at, on: :create
  after_update :touch_last_active
  
  # Instance methods
  def owner?
    role == 'owner'
  end
  
  def admin?
    role.in?(%w[owner admin])
  end
  
  def can_manage_projects?
    role.in?(%w[owner admin])
  end
  
  def can_view_analytics?
    role.in?(%w[owner admin member])
  end
  
  def can_export_data?
    role.in?(%w[owner admin])
  end
  
  def can_manage_users?
    role == 'owner'
  end
  
  def display_role
    role.humanize
  end
  
  def active_duration
    return nil unless last_active_at
    Time.current - last_active_at
  end
  
  private
  
  def set_joined_at
    self.joined_at ||= Time.current
  end
  
  def touch_last_active
    return unless active_changed? && active?
    touch(:last_active_at)
  end
end
