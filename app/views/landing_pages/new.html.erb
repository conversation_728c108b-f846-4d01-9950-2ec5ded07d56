<!-- <PERSON> Header -->
<div class="bg-white shadow-sm border-b border-slate-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
      <div class="flex items-center space-x-4">
        <%= link_to project_landing_pages_path(@project), 
                    class: "inline-flex items-center text-slate-500 hover:text-slate-700 transition-colors" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Back to Landing Pages
        <% end %>
        
        <div>
          <h1 class="text-2xl font-bold text-slate-900">Create Landing Page</h1>
          <p class="text-sm text-slate-500 mt-1">Build a beautiful landing page for <strong><%= @project.name %></strong></p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Form -->
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= form_with model: [@project, @landing_page], local: true, class: "space-y-8" do |form| %>
    <!-- Error Messages -->
    <% if @landing_page.errors.any? %>
      <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-red-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div>
            <h3 class="text-sm font-medium text-red-800">
              <%= pluralize(@landing_page.errors.count, "error") %> prohibited this landing page from being saved:
            </h3>
            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
              <% @landing_page.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Basic Information -->
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-slate-200">
        <h2 class="text-lg font-semibold text-slate-900">Basic Information</h2>
        <p class="text-sm text-slate-500 mt-1">Essential details for your landing page</p>
      </div>
      
      <div class="p-6 space-y-6">
        <div>
          <%= form.label :title, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_field :title, 
                              class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                              placeholder: "My Awesome Product Launch" %>
          <p class="mt-1 text-sm text-slate-500">The main headline visitors will see</p>
        </div>

        <div>
          <%= form.label :subtitle, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_field :subtitle, 
                              class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                              placeholder: "Join the waitlist to be the first to know" %>
          <p class="mt-1 text-sm text-slate-500">Supporting text under your main headline (optional)</p>
        </div>

        <div>
          <%= form.label :description, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_area :description, 
                             rows: 4,
                             class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                             placeholder: "Describe what makes your product special and why people should join your waitlist..." %>
          <p class="mt-1 text-sm text-slate-500">Detailed description of your product or service</p>
        </div>

        <div>
          <%= form.label :hero_image_url, "Hero Image URL", class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.url_field :hero_image_url, 
                             class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                             placeholder: "https://example.com/hero-image.jpg" %>
          <p class="mt-1 text-sm text-slate-500">Main image displayed prominently on your landing page</p>
        </div>
      </div>
    </div>

    <!-- Design & Branding -->
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-slate-200">
        <h2 class="text-lg font-semibold text-slate-900">Design & Branding</h2>
        <p class="text-sm text-slate-500 mt-1">Customize the look and feel</p>
      </div>
      
      <div class="p-6 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :primary_color, "Primary Color", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <div class="flex items-center space-x-3">
              <div class="relative">
                <%= form.color_field :primary_color, 
                                     value: @landing_page.primary_color || "#3B82F6",
                                     class: "w-12 h-10 border border-slate-300 rounded cursor-pointer" %>
              </div>
              <%= form.text_field :primary_color, 
                                  value: @landing_page.primary_color || "#3B82F6",
                                  class: "flex-1 px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                                  placeholder: "#3B82F6" %>
            </div>
            <p class="mt-1 text-sm text-slate-500">Main brand color for buttons and accents</p>
          </div>

          <div>
            <%= form.label :secondary_color, "Secondary Color", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <div class="flex items-center space-x-3">
              <div class="relative">
                <%= form.color_field :secondary_color, 
                                     value: @landing_page.secondary_color || "#10B981",
                                     class: "w-12 h-10 border border-slate-300 rounded cursor-pointer" %>
              </div>
              <%= form.text_field :secondary_color, 
                                  value: @landing_page.secondary_color || "#10B981",
                                  class: "flex-1 px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                                  placeholder: "#10B981" %>
            </div>
            <p class="mt-1 text-sm text-slate-500">Supporting color for highlights and features</p>
          </div>
        </div>

        <div>
          <%= form.label :custom_css, "Custom CSS", class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_area :custom_css, 
                             rows: 6,
                             class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 font-mono text-sm",
                             placeholder: "/* Add your custom styles here */\n.hero-section {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}" %>
          <p class="mt-1 text-sm text-slate-500">Advanced: Add custom CSS to override default styles</p>
        </div>
      </div>
    </div>

    <!-- SEO Settings -->
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
        <div>
          <h2 class="text-lg font-semibold text-slate-900">SEO Settings</h2>
          <p class="text-sm text-slate-500 mt-1">Optimize for search engines and social sharing</p>
        </div>
        <button type="button" id="seo-toggle-btn" class="text-sm text-blue-600 hover:text-blue-700 font-medium">
          <span id="seo-toggle-text">Show Advanced</span>
        </button>
      </div>
      
      <div id="seo-section" class="hidden p-6 space-y-6">
        <!-- Meta Tags -->
        <div class="space-y-4">
          <h3 class="text-md font-medium text-slate-800">Meta Tags</h3>
          
          <div>
            <%= form.label :meta_title, "Meta Title", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= form.text_field :meta_title, 
                                class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                                placeholder: "Auto-generated from title if left blank",
                                maxlength: 60 %>
            <p class="mt-1 text-sm text-slate-500">Shown in search results (60 characters max)</p>
          </div>

          <div>
            <%= form.label :meta_description, "Meta Description", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= form.text_area :meta_description, 
                               rows: 3,
                               class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                               placeholder: "Auto-generated from description if left blank",
                               maxlength: 160 %>
            <p class="mt-1 text-sm text-slate-500">Shown in search results (160 characters max)</p>
          </div>

          <div>
            <%= form.label :meta_keywords, "Meta Keywords", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= form.text_field :meta_keywords, 
                                class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                                placeholder: "waitlist, product launch, early access" %>
            <p class="mt-1 text-sm text-slate-500">Comma-separated keywords (auto-generated if left blank)</p>
          </div>

          <div>
            <%= form.label :canonical_url, "Canonical URL", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= form.url_field :canonical_url, 
                               class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                               placeholder: "https://example.com/product-launch" %>
            <p class="mt-1 text-sm text-slate-500">Preferred URL for search engines (auto-generated if left blank)</p>
          </div>
        </div>

        <!-- Open Graph -->
        <div class="space-y-4 pt-6 border-t border-slate-200">
          <h3 class="text-md font-medium text-slate-800">Open Graph (Facebook)</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= form.label :og_title, "OG Title", class: "block text-sm font-medium text-slate-700 mb-2" %>
              <%= form.text_field :og_title, 
                                  class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                                  placeholder: "Auto-generated if left blank",
                                  maxlength: 60 %>
            </div>

            <div>
              <%= form.label :og_image_url, "OG Image URL", class: "block text-sm font-medium text-slate-700 mb-2" %>
              <%= form.url_field :og_image_url, 
                                 class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                                 placeholder: "https://example.com/og-image.jpg" %>
            </div>
          </div>

          <div>
            <%= form.label :og_description, "OG Description", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= form.text_area :og_description, 
                               rows: 2,
                               class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                               placeholder: "Auto-generated if left blank",
                               maxlength: 160 %>
          </div>

          <div>
            <%= form.label :og_type, "OG Type", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= form.select :og_type, 
                            options_for_select([
                              ['Website', 'website'],
                              ['Product', 'product'],
                              ['Article', 'article'],
                              ['Business', 'business.business']
                            ], @landing_page.og_type || 'website'),
                            {},
                            { class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500" } %>
          </div>
        </div>

        <!-- Twitter Cards -->
        <div class="space-y-4 pt-6 border-t border-slate-200">
          <h3 class="text-md font-medium text-slate-800">Twitter Cards</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= form.label :twitter_title, "Twitter Title", class: "block text-sm font-medium text-slate-700 mb-2" %>
              <%= form.text_field :twitter_title, 
                                  class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                                  placeholder: "Auto-generated if left blank",
                                  maxlength: 60 %>
            </div>

            <div>
              <%= form.label :twitter_image_url, "Twitter Image URL", class: "block text-sm font-medium text-slate-700 mb-2" %>
              <%= form.url_field :twitter_image_url, 
                                 class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                                 placeholder: "https://example.com/twitter-image.jpg" %>
            </div>
          </div>

          <div>
            <%= form.label :twitter_description, "Twitter Description", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= form.text_area :twitter_description, 
                               rows: 2,
                               class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                               placeholder: "Auto-generated if left blank",
                               maxlength: 160 %>
          </div>

          <div>
            <%= form.label :twitter_card, "Twitter Card Type", class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= form.select :twitter_card, 
                            options_for_select([
                              ['Summary Card', 'summary'],
                              ['Summary with Large Image', 'summary_large_image'],
                              ['App Card', 'app'],
                              ['Player Card', 'player']
                            ], @landing_page.twitter_card || 'summary_large_image'),
                            {},
                            { class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500" } %>
          </div>
        </div>
      </div>
    </div>

    <!-- Publishing Settings -->
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-slate-200">
        <h2 class="text-lg font-semibold text-slate-900">Publishing Settings</h2>
        <p class="text-sm text-slate-500 mt-1">Control when and how your landing page goes live</p>
      </div>
      
      <div class="p-6 space-y-6">
        <div>
          <%= form.label :custom_domain, "Custom Domain", class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_field :custom_domain, 
                              class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                              placeholder: "myproduct.com" %>
          <p class="mt-1 text-sm text-slate-500">Optional: Use your own domain instead of the default URL</p>
        </div>

        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :published, 
                               class: "w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500" %>
          </div>
          <div class="ml-3">
            <%= form.label :published, class: "text-sm font-medium text-slate-700" do %>
              Publish immediately
            <% end %>
            <p class="text-sm text-slate-500">Make this landing page publicly accessible right away</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-end space-x-4 pt-6">
      <%= link_to "Cancel", project_landing_pages_path(@project), 
                  class: "px-6 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 hover:bg-slate-50 transition-colors" %>
      
      <%= form.submit "Save as Draft", 
                      name: "draft",
                      class: "px-6 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 hover:bg-slate-50 transition-colors" %>
      
      <%= form.submit "Create & Publish", 
                      class: "px-6 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors" %>
    </div>
  <% end %>
</div>

<script>
document.addEventListener('turbo:load', function() {
  // SEO Section Toggle
  function initSeoToggle() {
    const toggleBtn = document.getElementById('seo-toggle-btn');
    const section = document.getElementById('seo-section');
    const toggleText = document.getElementById('seo-toggle-text');
    
    if (toggleBtn && section && toggleText) {
      toggleBtn.addEventListener('click', function() {
        if (section.classList.contains('hidden')) {
          section.classList.remove('hidden');
          toggleText.textContent = 'Hide Advanced';
        } else {
          section.classList.add('hidden');
          toggleText.textContent = 'Show Advanced';
        }
      });
    }
  }
  
  // Color Picker Sync
  function initColorPickers() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    
    colorInputs.forEach(colorInput => {
      const textInput = colorInput.parentNode.nextElementSibling;
      
      if (textInput) {
        colorInput.addEventListener('change', function() {
          textInput.value = this.value;
        });
        
        textInput.addEventListener('input', function() {
          if (/^#[0-9A-F]{6}$/i.test(this.value)) {
            colorInput.value = this.value;
          }
        });
      }
    });
  }
  
  // Initialize all functionality
  initSeoToggle();
  initColorPickers();
});
</script>
