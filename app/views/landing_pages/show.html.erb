<!-- <PERSON> Header -->
<div class="bg-white shadow-sm border-b border-slate-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to project_landing_pages_path(@project), 
                      class: "inline-flex items-center text-slate-500 hover:text-slate-700 transition-colors" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Landing Pages
          <% end %>
          
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-slate-900"><%= @landing_page.title %></h1>
              <div class="flex items-center space-x-4 text-sm text-slate-500">
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  <%= pluralize(@landing_page.page_views, 'view') %>
                </span>
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                  </svg>
                  <%= pluralize(@landing_page.waitlist_entries.count, 'signup') %>
                </span>
                <span class="flex items-center">
                  <div class="w-2 h-2 <%= @landing_page.published? ? 'bg-green-400' : 'bg-yellow-400' %> rounded-full mr-2"></div>
                  <%= @landing_page.published? ? 'Published' : 'Draft' %>
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <%= link_to preview_project_landing_page_path(@project, @landing_page), 
                      target: "_blank",
                      class: "inline-flex items-center px-4 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            Preview
          <% end %>
          
          <%= link_to edit_project_landing_page_path(@project, @landing_page), 
                      class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="grid lg:grid-cols-3 gap-8">
    
    <!-- Main Column -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Landing Page Details -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Landing Page Details</h2>
        </div>
        <div class="p-6 space-y-6">
          
          <!-- Title & Subtitle -->
          <div>
            <h3 class="text-2xl font-bold text-slate-900 mb-2"><%= @landing_page.title %></h3>
            <% if @landing_page.subtitle.present? %>
              <p class="text-xl text-slate-600"><%= @landing_page.subtitle %></p>
            <% end %>
          </div>
          
          <!-- Description -->
          <% if @landing_page.description.present? %>
            <div>
              <h4 class="text-sm font-semibold text-slate-900 mb-2">Description</h4>
              <div class="prose text-slate-600 max-w-none">
                <%= simple_format(@landing_page.description) %>
              </div>
            </div>
          <% end %>
          
          <!-- Design Settings -->
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <h4 class="text-sm font-semibold text-slate-900 mb-3">Colors</h4>
              <div class="space-y-2">
                <div class="flex items-center space-x-3">
                  <div class="w-6 h-6 rounded border border-slate-200" style="background-color: <%= @landing_page.primary_color %>"></div>
                  <span class="text-sm text-slate-600">Primary: <%= @landing_page.primary_color %></span>
                </div>
                <div class="flex items-center space-x-3">
                  <div class="w-6 h-6 rounded border border-slate-200" style="background-color: <%= @landing_page.secondary_color %>"></div>
                  <span class="text-sm text-slate-600">Secondary: <%= @landing_page.secondary_color %></span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 class="text-sm font-semibold text-slate-900 mb-3">URLs</h4>
              <div class="space-y-2">
                <div>
                  <span class="text-sm text-slate-500">Slug:</span>
                  <span class="text-sm text-slate-900 font-mono"><%= @landing_page.slug %></span>
                </div>
                <% if @landing_page.custom_domain.present? %>
                  <div>
                    <span class="text-sm text-slate-500">Custom Domain:</span>
                    <span class="text-sm text-slate-900 font-mono"><%= @landing_page.custom_domain %></span>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
          
          <!-- Hero Image -->
          <% if @landing_page.hero_image_url.present? %>
            <div>
              <h4 class="text-sm font-semibold text-slate-900 mb-3">Hero Image</h4>
              <div class="rounded-lg overflow-hidden border border-slate-200">
                <img src="<%= @landing_page.hero_image_url %>" 
                     alt="Hero image" 
                     class="w-full h-48 object-cover">
              </div>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Recent Signups -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
          <h2 class="text-lg font-semibold text-slate-900">Recent Signups</h2>
          <% if @analytics[:total_signups] > 0 %>
            <%= link_to project_landing_page_waitlist_entries_path(@project, @landing_page),
                        class: "text-sm text-blue-600 hover:text-blue-700 font-medium" do %>
              View all <%= @analytics[:total_signups] %> →
            <% end %>
          <% end %>
        </div>
        <div class="p-6">
          <% if @analytics[:recent_signups].any? %>
            <div class="space-y-4">
              <% @analytics[:recent_signups].each do |entry| %>
                <div class="flex items-center justify-between py-3 border-b border-slate-100 last:border-b-0">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-sm font-medium">
                        <%= entry.email.first.upcase %>
                      </span>
                    </div>
                    <div>
                      <div class="font-medium text-slate-900"><%= entry.email %></div>
                      <div class="text-sm text-slate-500"><%= time_ago_in_words(entry.created_at) %> ago</div>
                    </div>
                  </div>
                  <% if entry.confirmed? %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      Confirmed
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                      </svg>
                      Pending
                    </span>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
              <h3 class="text-lg font-medium text-slate-900 mb-2">No signups yet</h3>
              <p class="text-slate-500 mb-4">Share your landing page to start collecting signups.</p>
              <% if @landing_page.published? %>
                <button onclick="navigator.clipboard.writeText('<%= @landing_page.full_url %>')" 
                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                  Copy Link
                </button>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    
    <!-- Sidebar -->
    <div class="space-y-6">
      
      <!-- Analytics Overview -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Analytics</h2>
        </div>
        <div class="p-6 space-y-6">
          
          <!-- Key Metrics -->
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600"><%= @analytics[:page_views] %></div>
              <div class="text-sm text-slate-500">Page Views</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600"><%= @analytics[:total_signups] %></div>
              <div class="text-sm text-slate-500">Signups</div>
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600"><%= @analytics[:unique_visitors] %></div>
              <div class="text-sm text-slate-500">Unique Visitors</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-600"><%= @analytics[:conversion_rate] %>%</div>
              <div class="text-sm text-slate-500">Conversion</div>
            </div>
          </div>
          
          <!-- Conversion Rate Progress -->
          <div>
            <div class="flex justify-between text-sm mb-2">
              <span class="text-slate-600">Conversion Rate</span>
              <span class="font-medium text-slate-900"><%= @analytics[:conversion_rate] %>%</span>
            </div>
            <div class="w-full bg-slate-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-300" 
                   style="width: <%= [@analytics[:conversion_rate], 100].min %>%"></div>
            </div>
            <div class="text-xs text-slate-500 mt-1">Industry average: 2-5%</div>
          </div>
          
          <!-- View All Button -->
          <% if @analytics[:total_signups] > 0 %>
            <%= link_to project_landing_page_waitlist_entries_path(@project, @landing_page),
                        class: "w-full mt-4 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
              </svg>
              Manage Waitlist
            <% end %>
          <% end %>
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Quick Actions</h2>
        </div>
        <div class="p-6 space-y-3">
          
          <%= link_to toggle_published_project_landing_page_path(@project, @landing_page), 
                      method: :patch,
                      data: { turbo_method: :patch },
                      class: "flex items-center w-full px-4 py-3 text-left rounded-lg hover:bg-slate-50 transition-colors border border-slate-200" do %>
            <% if @landing_page.published? %>
              <svg class="w-5 h-5 mr-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L12 21l-4.95-4.95m0 0L5.636 5.636M7.05 16.05L12 21l4.95-4.95m0 0L18.364 5.636"></path>
              </svg>
              <span class="font-medium text-slate-900">Unpublish</span>
            <% else %>
              <svg class="w-5 h-5 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="font-medium text-slate-900">Publish</span>
            <% end %>
          <% end %>
          
          <%= link_to duplicate_project_landing_page_path(@project, @landing_page), 
                      method: :post,
                      data: { turbo_method: :post },
                      class: "flex items-center w-full px-4 py-3 text-left rounded-lg hover:bg-slate-50 transition-colors border border-slate-200" do %>
            <svg class="w-5 h-5 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            <span class="font-medium text-slate-900">Duplicate</span>
          <% end %>
          
          <%= link_to [@project, @landing_page], 
                      method: :delete,
                      data: { 
                        turbo_method: :delete,
                        turbo_confirm: "Are you sure you want to delete this landing page?"
                      },
                      class: "flex items-center w-full px-4 py-3 text-left rounded-lg hover:bg-red-50 transition-colors border border-red-200 text-red-600" do %>
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            <span class="font-medium">Delete</span>
          <% end %>
        </div>
      </div>
      
      <!-- SEO Status -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">SEO Status</h2>
        </div>
        <div class="p-6 space-y-4">
          
          <!-- SEO Score visualization -->
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600 mb-1">85</div>
            <div class="text-sm text-slate-500">SEO Score</div>
            <div class="w-full bg-slate-200 rounded-full h-2 mt-2">
              <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
            </div>
          </div>
          
          <!-- SEO Checklist -->
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <span class="text-sm text-slate-600">Meta Title</span>
              <svg class="w-4 h-4 <%= @landing_page.meta_title.present? ? 'text-green-500' : 'text-red-500' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <% if @landing_page.meta_title.present? %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                <% end %>
              </svg>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm text-slate-600">Meta Description</span>
              <svg class="w-4 h-4 <%= @landing_page.meta_description.present? ? 'text-green-500' : 'text-red-500' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <% if @landing_page.meta_description.present? %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                <% end %>
              </svg>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm text-slate-600">Open Graph</span>
              <svg class="w-4 h-4 <%= @landing_page.og_title.present? ? 'text-green-500' : 'text-red-500' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <% if @landing_page.og_title.present? %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                <% end %>
              </svg>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm text-slate-600">Published</span>
              <svg class="w-4 h-4 <%= @landing_page.published? ? 'text-green-500' : 'text-red-500' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <% if @landing_page.published? %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                <% end %>
              </svg>
            </div>
          </div>
          
          <% if @landing_page.last_crawled_at.present? %>
            <div class="text-xs text-slate-500 pt-2 border-t border-slate-100">
              Last crawled: <%= time_ago_in_words(@landing_page.last_crawled_at) %> ago
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
