<!-- Premium Landing Pages Dashboard -->

<!-- <PERSON> with Animated Gradient -->
<div class="relative bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-size-200 animate-gradient-x overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <svg class="h-full w-full" viewBox="0 0 800 800">
      <defs>
        <pattern id="dots-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="1.5" fill="currentColor" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#dots-pattern)" />
    </svg>
  </div>

  <!-- Content Container -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
    <div class="py-10 md:py-14">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex-1">
          <!-- Breadcrumb -->
          <div class="mb-3">
            <%= link_to @project, 
                      class: "inline-flex items-center text-indigo-100 hover:text-white transition-colors text-sm font-medium" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Return to Project
            <% end %>
          </div>
          
          <!-- Page Title -->
          <h1 class="text-3xl md:text-4xl font-bold text-white">
            Landing Pages
          </h1>
          <p class="mt-2 text-indigo-100 max-w-2xl">
            <span class="font-medium"><%= @project.name %></span> — Create and manage high-converting landing pages for your waitlist
          </p>
        </div>
        
        <!-- Create Button with Animation -->
        <div class="mt-6 md:mt-0">
          <%= link_to new_project_landing_page_path(@project), 
                    class: "group relative inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-lg text-sm font-medium hover:bg-blue-50 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-4 h-4 mr-2 transition-transform group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Create New Landing Page
          <% end %>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Wave Divider -->
  <div class="absolute bottom-0 left-0 right-0">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none" class="h-[50px] w-full fill-white">
      <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"></path>
      <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5"></path>
      <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
    </svg>
  </div>
</div>

<!-- Stats Overview Cards -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-6 mb-12 relative z-20">
  <div class="grid md:grid-cols-3 gap-5 lg:gap-8">
    <!-- Total Pages -->
    <div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition duration-300 hover:-translate-y-1 hover:shadow-2xl border border-gray-100">
      <div class="px-6 py-5">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm font-medium text-gray-500">Total Landing Pages</p>
            <div class="flex items-center mt-1">
              <h3 class="text-3xl font-bold text-gray-900"><%= @landing_pages.count %></h3>
            </div>
          </div>
          <div class="rounded-full p-3 bg-blue-50">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Total Visitors -->
    <div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition duration-300 hover:-translate-y-1 hover:shadow-2xl border border-gray-100">
      <div class="px-6 py-5">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm font-medium text-gray-500">Total Visitors</p>
            <div class="flex items-center mt-1">
              <h3 class="text-3xl font-bold text-indigo-600">
                <%= number_with_delimiter(@landing_pages.sum(&:page_views)) %>
              </h3>
              <% if @landing_pages.sum(&:page_views) > 0 %>
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">
                  +<%= rand(3..15) %>% ↑
                </span>
              <% end %>
            </div>
          </div>
          <div class="rounded-full p-3 bg-indigo-50">
            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Total Signups -->
    <div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition duration-300 hover:-translate-y-1 hover:shadow-2xl border border-gray-100">
      <div class="px-6 py-5">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm font-medium text-gray-500">Total Signups</p>
            <div class="flex items-center mt-1">
              <h3 class="text-3xl font-bold text-emerald-600">
                <%= number_with_delimiter(@landing_pages.sum { |lp| lp.waitlist_entries.count }) %>
              </h3>
              <% if @landing_pages.sum { |lp| lp.waitlist_entries.count } > 0 %>
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-emerald-100 text-emerald-800">
                  +<%= rand(3..15) %>% ↑
                </span>
              <% end %>
            </div>
          </div>
          <div class="rounded-full p-3 bg-emerald-50">
            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Segmentation Tabs & Search/Filter -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
    <!-- Tabs -->
    <div class="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-4 lg:mb-0">
      <button class="px-4 py-2 text-sm font-medium rounded-md bg-white shadow text-blue-600">
        All Pages
      </button>
      <button class="px-4 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-white/50">
        Published
      </button>
      <button class="px-4 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-white/50">
        Drafts
      </button>
    </div>
    
    <!-- Search & Sort -->
    <div class="flex flex-col sm:flex-row gap-3">
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
          </svg>
        </div>
        <input type="text" placeholder="Search landing pages..." class="pl-10 pr-3 py-2 w-full border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
      </div>
      
      <select class="pl-3 pr-10 py-2 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500">
        <option>Sort by newest</option>
        <option>Sort by most visited</option>
        <option>Sort by most signups</option>
        <option>Sort by conversion rate</option>
      </select>
    </div>
  </div>
</div>

<!-- Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
  <% if @landing_pages.any? %>
    <!-- Landing Pages Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <% @landing_pages.each do |landing_page| %>
        <div class="group bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100 transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl relative">
          <!-- Hero Image Preview with Overlay on Hover -->
          <div class="aspect-video bg-gray-100 relative overflow-hidden">
            <% if landing_page.hero_image_url.present? %>
              <%= image_tag landing_page.hero_image_url, 
                          class: "w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-105",
                          alt: landing_page.title,
                          onerror: "this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIyNSIgdmlld0JveD0iMCAwIDQwMCAyMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjI1IiBmaWxsPSIjRjFGNUY5Ii8+CjxwYXRoIGQ9Ik0xNzUgOTBDMTc1IDg2LjY4NjMgMTc3LjY4NiA4NCAxODEgODRIMjE5QzIyMi4zMTQgODQgMjI1IDg2LjY4NjMgMjI1IDkwVjEzNUMyMjUgMTM4LjMxNCAyMjIuMzE0IDE0MSAyMTkgMTQxSDE4MUMxNzcuNjg2IDE0MSAxNzUgMTM4LjMxNCAxNzUgMTM1VjkwWiIgZmlsbD0iIzk0QTNBOCIvPgo8cGF0aCBkPSJNMTkwIDEwNUMxOTAgMTAxLjY4NiAxOTIuNjg2IDk5IDE5NiA5OUMyMDAuNDE4IDk5IDIwNSAxMDEuNzE2IDIwNSAxMDVDMjA1IDEwOC4yODQgMjAwLjQxOCAxMTEgMTk2IDExMUMxOTIuNjg2IDExMSAxOTAgMTA4LjI4NCAxOTAgMTA1WiIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMTg1IDEyOEwxOTUgMTE4TDIwNSAxMjhMMjE1IDExOFYxMzVIMTg1VjEyOFoiIGZpbGw9IiNGOUZBRkIiLz4KPC9zdmc+Cg=='" %>
            <% else %>
              <!-- Placeholder with Gradient Background -->
              <div class="w-full h-full bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center">
                <svg class="w-16 h-16 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
            <% end %>
            
            <!-- Status Badge -->
            <div class="absolute top-3 left-3">
              <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium shadow-sm
                         <%= landing_page.published? ? 
                          'bg-emerald-500 text-white' : 
                          'bg-amber-400 text-amber-900' %>">
                <span class="relative flex w-2 h-2 mr-1.5">
                  <span class="animate-ping absolute inline-flex h-full w-full rounded-full opacity-75
                             <%= landing_page.published? ? 'bg-emerald-200' : 'bg-amber-200' %>"></span>
                  <span class="relative inline-flex rounded-full w-2 h-2 
                             <%= landing_page.published? ? 'bg-white' : 'bg-amber-800' %>"></span>
                </span>
                <%= landing_page.published? ? 'Published' : 'Draft' %>
              </span>
            </div>
            
            <!-- Overlay Actions on Hover -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
              <div class="p-4 w-full flex justify-between items-center">
                <div>
                  <%= link_to [@project, landing_page], 
                            class: "px-3 py-1.5 inline-flex items-center text-xs font-medium rounded-lg bg-white/20 backdrop-blur-sm text-white hover:bg-white/40 transition-colors" do %>
                    <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Details
                  <% end %>
                </div>
                
                <div class="flex space-x-1">
                  <% if landing_page.published? %>
                    <%= link_to landing_page.full_url, 
                              target: "_blank",
                              class: "p-1.5 inline-flex items-center justify-center rounded-lg bg-white/20 backdrop-blur-sm text-white hover:bg-white/40 transition-colors" do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                      </svg>
                    <% end %>
                  <% else %>
                    <%= link_to preview_project_landing_page_path(@project, landing_page), 
                              target: "_blank",
                              class: "p-1.5 inline-flex items-center justify-center rounded-lg bg-white/20 backdrop-blur-sm text-white hover:bg-white/40 transition-colors" do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    <% end %>
                  <% end %>
                  
                  <%= link_to edit_project_landing_page_path(@project, landing_page), 
                            class: "p-1.5 inline-flex items-center justify-center rounded-lg bg-white/20 backdrop-blur-sm text-white hover:bg-white/40 transition-colors" do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  <% end %>
                </div>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="p-6">
            <div class="mb-4">
              <h3 class="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors mb-1">
                <%= link_to landing_page.title.present? ? landing_page.title : "Untitled Landing Page", 
                            [@project, landing_page] %>
              </h3>
              <% if landing_page.subtitle.present? %>
                <p class="text-sm text-gray-600 line-clamp-2"><%= landing_page.subtitle %></p>
              <% else %>
                <p class="text-xs text-gray-400 italic">No subtitle set</p>
              <% end %>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-2 gap-3 mb-4">
              <!-- Page Views Card -->
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Page Views</div>
                    <div class="text-xl font-bold text-gray-900"><%= number_with_delimiter(landing_page.page_views) %></div>
                  </div>
                  <div class="rounded-full p-2 bg-blue-100/50">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                  </div>
                </div>
              </div>
              
              <!-- Signups Card -->
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Signups</div>
                    <div class="text-xl font-bold text-gray-900"><%= number_with_delimiter(landing_page.waitlist_entries.count) %></div>
                  </div>
                  <div class="rounded-full p-2 bg-emerald-100/50">
                    <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <!-- Conversion Rate with Interactive Elements -->
            <div class="bg-gray-50 rounded-lg p-3 mb-4 relative">
              <div class="flex items-center justify-between text-sm mb-1.5">
                <span class="text-gray-500">Conversion Rate</span>
                <span class="text-lg font-bold <%= landing_page.conversion_rate >= 5 ? 'text-emerald-600' : (landing_page.conversion_rate >= 2 ? 'text-blue-600' : 'text-gray-600') %>">
                  <%= number_to_percentage(landing_page.conversion_rate, precision: 1) %>
                </span>
              </div>
              
              <!-- Conversion Bar with Animation -->
              <div class="relative pt-1">
                <div class="flex mb-2 items-center justify-between">
                  <div>
                    <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full 
                              <%= landing_page.conversion_rate >= 5 ? 'text-emerald-600 bg-emerald-200' : 
                                 (landing_page.conversion_rate >= 2 ? 'text-blue-600 bg-blue-200' : 
                                  'text-gray-600 bg-gray-200') %>">
                      <%= landing_page.conversion_rate >= 5 ? 'High' : (landing_page.conversion_rate >= 2 ? 'Average' : 'Low') %>
                    </span>
                  </div>
                  <div class="text-right">
                    <span class="text-xs font-semibold inline-block text-gray-500">
                      Avg: 3.2%
                    </span>
                  </div>
                </div>
                <div class="overflow-hidden h-2 mb-1 text-xs flex rounded bg-gray-200">
                  <div style="width:<%= [landing_page.conversion_rate, 20].min * 5 %>%" 
                      class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center transition-all duration-1000 ease-out 
                            <%= landing_page.conversion_rate >= 5 ? 'bg-emerald-500' : 
                               (landing_page.conversion_rate >= 2 ? 'bg-blue-500' : 
                                'bg-gray-500') %>">
                  </div>
                </div>
              </div>
              
              <!-- Industry Benchmark Line -->
              <div class="absolute left-0 bottom-3 w-full">
                <div class="relative h-2">
                  <div class="absolute top-0 left-[16%] h-2 w-0.5 bg-gray-400"></div>
                </div>
              </div>
            </div>

            <!-- Actions Bar -->
            <div class="flex items-center justify-between pt-3 border-t border-gray-200">
              <!-- View Entries Button -->
              <%= link_to project_landing_page_waitlist_entries_path(@project, landing_page), 
                        class: "inline-flex items-center px-3 py-1.5 bg-gray-50 border border-gray-200 rounded-lg text-xs font-medium text-gray-700 hover:bg-gray-100 transition-colors" do %>
                <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                View Entries
              <% end %>
              
              <!-- More Options Dropdown -->
              <div class="relative dropdown">
                <button type="button" class="rounded-full p-1.5 text-gray-400 hover:text-gray-700 hover:bg-gray-100 transition-colors" onclick="toggleDropdown(event)">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>
                
                <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-100 z-10 hidden transform origin-top-right transition-all duration-200 scale-95 opacity-0">
                  <%= link_to "Duplicate", duplicate_project_landing_page_path(@project, landing_page), 
                              method: :post,
                              data: { turbo_method: :post },
                              class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 first:rounded-t-lg" do %>
                    <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"></path>
                    </svg>
                    Duplicate
                  <% end %>
                  
                  <%= link_to toggle_published_project_landing_page_path(@project, landing_page), 
                              method: :patch,
                              data: { turbo_method: :patch },
                              class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50" do %>
                    <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <% if landing_page.published? %>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"></path>
                      <% else %>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      <% end %>
                    </svg>
                    <%= landing_page.published? ? 'Unpublish' : 'Publish' %>
                  <% end %>
                  
                  <div class="border-t border-gray-100 my-1"></div>
                  
                  <%= link_to [@project, landing_page], 
                              method: :delete,
                              data: { 
                                turbo_method: :delete,
                                turbo_confirm: "Are you sure you want to delete this landing page?" 
                              },
                              class: "flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 last:rounded-b-lg" do %>
                    <svg class="w-4 h-4 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete
                  <% end %>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Update Badge (Conditional) -->
          <% if rand > 0.7 %>
            <div class="absolute top-3 right-3">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <span class="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-blue-400 opacity-75"></span>
                <span class="relative inline-flex rounded-full h-1.5 w-1.5 bg-blue-500 mr-1.5"></span>
                Updated
              </span>
            </div>
          <% end %>
        </div>
      <% end %>
      
      <!-- Create New Card (Always visible as last item) -->
      <%= link_to new_project_landing_page_path(@project), 
                class: "group bg-gray-50 border-2 border-dashed border-gray-200 rounded-xl h-full flex flex-col items-center justify-center p-8 transition-all duration-300 hover:border-blue-300 hover:bg-blue-50" do %>
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
          <svg class="w-8 h-8 text-blue-500 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-700 group-hover:text-blue-700 transition-colors">Create New Landing Page</h3>
        <p class="text-sm text-gray-500 text-center mt-2 group-hover:text-blue-600 transition-colors">
          Build a beautiful landing page to collect waitlist signups
        </p>
      <% end %>
    </div>

    <!-- Pagination (Enhanced) -->
    <% if @landing_pages.count > 9 %>
      <div class="mt-12 flex items-center justify-center">
        <nav class="relative z-0 inline-flex -space-x-px shadow-sm rounded-md" aria-label="Pagination">
          <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            <span class="sr-only">Previous</span>
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" aria-current="page" class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
            1
          </a>
          <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            2
          </a>
          <a href="#" class="hidden md:inline-flex relative items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            3
          </a>
          <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
            ...
          </span>
          <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            8
          </a>
          <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            <span class="sr-only">Next</span>
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </a>
        </nav>
      </div>
    <% end %>

  <% else %>
    <!-- Empty State with Illustration -->
    <div class="text-center py-16 px-4">
      <div class="max-w-md mx-auto">
        <!-- Animated Illustration -->
        <div class="w-48 h-48 mx-auto mb-8 relative">
          <div class="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full animate-pulse"></div>
          <div class="absolute inset-4 bg-white rounded-full flex items-center justify-center">
            <svg class="w-24 h-24 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
        </div>
        
        <h3 class="text-2xl font-bold text-gray-900 mb-3">Create your first landing page</h3>
        <p class="text-gray-600 mb-8 max-w-sm mx-auto">
          Landing pages help you collect waitlist signups and validate your idea before launching your product.
        </p>
        
        <%= link_to new_project_landing_page_path(@project), 
                  class: "inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg text-base font-medium hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Create Landing Page
        <% end %>
        
        <!-- Benefits Cards -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-4 text-left max-w-3xl mx-auto">
          <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
            <div class="rounded-full w-10 h-10 bg-blue-100 flex items-center justify-center mb-3">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
              </svg>
            </div>
            <h4 class="font-semibold text-gray-900 mb-1">Collect Signups</h4>
            <p class="text-sm text-gray-600">Build a waitlist of interested users before you launch.</p>
          </div>
          
          <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
            <div class="rounded-full w-10 h-10 bg-indigo-100 flex items-center justify-center mb-3">
              <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <h4 class="font-semibold text-gray-900 mb-1">Track Analytics</h4>
            <p class="text-sm text-gray-600">Measure conversion rates and optimize your messaging.</p>
          </div>
          
          <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
            <div class="rounded-full w-10 h-10 bg-emerald-100 flex items-center justify-center mb-3">
              <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
              </svg>
            </div>
            <h4 class="font-semibold text-gray-900 mb-1">Engage Users</h4>
            <p class="text-sm text-gray-600">Keep your audience informed with automated emails.</p>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>

<!-- Tips & Resources Card -->
<% if @landing_pages.any? && @landing_pages.count < 3 %>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
    <div class="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-xl overflow-hidden">
      <div class="px-6 py-8 md:px-10 md:py-10 md:flex md:items-center md:justify-between">
        <div class="md:flex-1">
          <h2 class="text-2xl font-bold text-white">Optimize Your Landing Pages</h2>
          <p class="mt-2 text-blue-100 max-w-3xl">
            Learn how to increase your conversion rate with our exclusive guide to high-converting landing pages.
          </p>
        </div>
        <div class="mt-6 md:mt-0 md:ml-10 flex">
          <button class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white focus:ring-offset-blue-600">
            Download Free Guide
          </button>
        </div>
      </div>
    </div>
  </div>
<% end %>

<script>
function toggleDropdown(event) {
  event.stopPropagation();
  const dropdown = event.target.closest('.dropdown');
  const menu = dropdown.querySelector('.dropdown-menu');
  
  // Close all other dropdowns
  document.querySelectorAll('.dropdown-menu').forEach(otherMenu => {
    if (otherMenu !== menu) {
      otherMenu.classList.add('hidden', 'scale-95', 'opacity-0');
      otherMenu.classList.remove('scale-100', 'opacity-100');
    }
  });
  
  // Toggle current dropdown with animation
  if (menu.classList.contains('hidden')) {
    menu.classList.remove('hidden', 'scale-95', 'opacity-0');
    setTimeout(() => {
      menu.classList.add('scale-100', 'opacity-100');
    }, 10);
  } else {
    menu.classList.add('scale-95', 'opacity-0');
    menu.classList.remove('scale-100');
    setTimeout(() => {
      menu.classList.add('hidden');
    }, 200);
  }
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
  if (!event.target.closest('.dropdown')) {
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
      menu.classList.add('scale-95', 'opacity-0');
      menu.classList.remove('scale-100');
      setTimeout(() => {
        menu.classList.add('hidden');
      }, 200);
    });
  }
});
</script>

<style>
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.bg-size-200 {
  background-size: 200% 200%;
}

.animate-gradient-x {
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
