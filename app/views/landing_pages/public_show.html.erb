<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
  <!-- Animated background elements -->
  <div class="absolute inset-0">
    <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
    <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
    <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
  </div>
  
  <!-- Grid pattern overlay -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(255,255,255,0.8) 1px, transparent 0); background-size: 40px 40px;"></div>
  </div>
  
  <div class="relative z-10 max-w-6xl mx-auto px-6 text-center">
    <!-- Badge with glow effect -->
    <div class="inline-flex items-center px-6 py-2 mb-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white text-sm font-medium shadow-lg">
      <span class="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse shadow-sm shadow-green-400/50"></span>
      🚀 Limited Early Access • Join Now
    </div>
    
    <!-- Main headline with gradient text -->
    <h1 class="text-5xl md:text-7xl font-extrabold mb-8 leading-tight">
      <span class="bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent drop-shadow-lg">
        <%= @landing_page.title %>
      </span>
    </h1>
    
    <!-- Subtitle -->
    <p class="text-xl md:text-2xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed font-light">
      <%= @landing_page.subtitle || @landing_page.description %>
    </p>
    
    <!-- Hero image if available -->
    <% if @landing_page.hero_image_url.present? %>
      <div class="mb-12">
        <div class="relative max-w-4xl mx-auto">
          <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-75"></div>
          <img src="<%= @landing_page.hero_image_url %>" 
               alt="<%= @landing_page.title %>" 
               class="relative w-full h-auto rounded-2xl shadow-2xl border border-white/20 backdrop-blur-sm">
        </div>
      </div>
    <% end %>
    
    <!-- Enhanced email signup form -->
    <div class="max-w-lg mx-auto mb-16">
      <%= form_with model: [@landing_page, WaitlistEntry.new], url: public_waitlist_entries_path(@landing_page), 
                    local: true, class: "space-y-6" do |form| %>
        
        <div class="relative">
          <div class="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20"></div>
          <div class="relative p-6">
            <div class="flex flex-col sm:flex-row gap-4">
              <%= form.email_field :email, 
                    placeholder: "Enter your email address", 
                    class: "flex-1 px-5 py-4 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/25 focus:border-blue-500 text-slate-900 placeholder-slate-500 transition-all duration-300 font-medium shadow-sm",
                    required: true %>
              
              <%= form.submit "Join Waitlist", 
                    class: "px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold rounded-xl transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
            </div>
            
            <div class="flex items-center justify-center mt-4 text-sm text-blue-100">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.18-5.18A2.121 2.121 0 0116 5a2.121 2.121 0 012.121 2.121c0 .738-.386 1.398-1.03 1.758L9.64 14.536A2.121 2.121 0 016 14a2.121 2.121 0 000-4.242c.738 0 1.398.386 1.758 1.03l5.657-7.45z"></path>
              </svg>
              ✨ Free forever • No spam • Unsubscribe anytime
            </div>
          </div>
        </div>
      <% end %>
    </div>
    
    <!-- Enhanced social proof with animations -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
      <div class="text-center p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
        <div class="text-3xl font-bold text-white mb-2 animate-pulse"><%= @waitlist_count %></div>
        <div class="text-blue-100 text-sm font-medium">Builders joined</div>
      </div>
      <div class="text-center p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
        <div class="text-3xl font-bold text-white mb-2">⚡</div>
        <div class="text-blue-100 text-sm font-medium">Launch soon</div>
      </div>
      <div class="text-center p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
        <div class="text-3xl font-bold text-white mb-2">🎯</div>
        <div class="text-blue-100 text-sm font-medium">Early bird perks</div>
      </div>
    </div>
  </div>
  
  <!-- Scroll indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <svg class="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
    </svg>
  </div>
</section>

<!-- Features Section -->
<section class="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute top-0 left-0 w-full h-full opacity-30">
    <div class="absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full blur-2xl"></div>
    <div class="absolute bottom-20 right-10 w-32 h-32 bg-purple-200 rounded-full blur-3xl"></div>
  </div>
  
  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <div class="text-center mb-20">
      <div class="inline-flex items-center px-4 py-2 mb-6 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
        ⭐ Why join our exclusive waitlist?
      </div>
      <h2 class="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
        Unlock <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Premium Benefits</span>
      </h2>
      <p class="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">Get exclusive early access and special benefits reserved for our founding members</p>
    </div>
    
    <div class="grid md:grid-cols-3 gap-8 lg:gap-12">
      <!-- Feature 1 -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
        <div class="relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100 group-hover:border-blue-200">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-slate-900 mb-4 group-hover:text-blue-600 transition-colors">🚀 VIP Early Access</h3>
          <p class="text-slate-600 leading-relaxed mb-4">Be among the first 100 users to experience our platform with exclusive beta features and priority support.</p>
          <div class="flex items-center text-sm text-blue-600 font-medium">
            <span class="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
            Limited to first 100 users
          </div>
        </div>
      </div>
      
      <!-- Feature 2 -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-r from-green-600 to-emerald-700 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
        <div class="relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100 group-hover:border-green-200">
          <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-slate-900 mb-4 group-hover:text-green-600 transition-colors">💰 Founder's Pricing</h3>
          <p class="text-slate-600 leading-relaxed mb-4">Lock in exclusive lifetime pricing at 50% off regular rates, plus free premium features forever.</p>
          <div class="flex items-center text-sm text-green-600 font-medium">
            <span class="w-2 h-2 bg-green-600 rounded-full mr-2"></span>
            Save 50% for life
          </div>
        </div>
      </div>
      
      <!-- Feature 3 -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-violet-700 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
        <div class="relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100 group-hover:border-purple-200">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-slate-900 mb-4 group-hover:text-purple-600 transition-colors">🎯 Shape the Future</h3>
          <p class="text-slate-600 leading-relaxed mb-4">Direct line to our product team. Your feedback shapes features, and you get early previews of new releases.</p>
          <div class="flex items-center text-sm text-purple-600 font-medium">
            <span class="w-2 h-2 bg-purple-600 rounded-full mr-2"></span>
            Direct product influence
          </div>
        </div>
      </div>
    </div>
    
    <!-- Extra benefits bar -->
    <div class="mt-16 p-8 bg-gradient-to-r from-slate-900 to-slate-800 rounded-2xl text-center">
      <h3 class="text-2xl font-bold text-white mb-4">Plus these exclusive bonuses:</h3>
      <div class="grid md:grid-cols-4 gap-6 text-slate-300">
        <div class="flex items-center justify-center space-x-2">
          <span class="text-yellow-400">👑</span>
          <span class="font-medium">Priority Support</span>
        </div>
        <div class="flex items-center justify-center space-x-2">
          <span class="text-green-400">📚</span>
          <span class="font-medium">Exclusive Resources</span>
        </div>
        <div class="flex items-center justify-center space-x-2">
          <span class="text-blue-400">🔒</span>
          <span class="font-medium">Private Community</span>
        </div>
        <div class="flex items-center justify-center space-x-2">
          <span class="text-purple-400">🎁</span>
          <span class="font-medium">Surprise Perks</span>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials -->
<section class="py-24 bg-white relative overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-purple-50/50"></div>
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full opacity-20 transform translate-x-48 -translate-y-48"></div>
  
  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <div class="text-center mb-20">
      <div class="inline-flex items-center px-4 py-2 mb-6 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 rounded-full text-sm font-semibold">
        💬 What early supporters are saying
      </div>
      <h2 class="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
        Join <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Happy Builders</span>
      </h2>
      <p class="text-xl text-slate-600 max-w-2xl mx-auto">Real feedback from excited early supporters who can't wait to get started</p>
    </div>
    
    <div class="grid md:grid-cols-3 gap-8 mb-16">
      <!-- Testimonial 1 -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-purple-600/5 rounded-2xl transform rotate-1 group-hover:rotate-0 transition-transform duration-300"></div>
        <div class="relative bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100">
          <!-- Quote icon -->
          <div class="absolute top-4 right-4 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z"/>
            </svg>
          </div>
          
          <div class="flex items-center mb-6">
            <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
              JS
            </div>
            <div class="ml-4">
              <div class="font-bold text-slate-900 text-lg">Jane Smith</div>
              <div class="text-blue-600 font-medium">Product Manager</div>
              <div class="text-sm text-slate-500">TechFlow Inc.</div>
            </div>
          </div>
          
          <p class="text-slate-700 leading-relaxed mb-4 text-lg">"Can't wait to see what's coming. The preview looks incredible and exactly what we need for our workflow."</p>
          
          <div class="flex items-center justify-between">
            <div class="flex text-yellow-400 text-lg">
              ⭐⭐⭐⭐⭐
            </div>
            <div class="text-xs text-slate-400 font-medium">2 days ago</div>
          </div>
        </div>
      </div>
      
      <!-- Testimonial 2 -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-br from-green-600/5 to-emerald-600/5 rounded-2xl transform -rotate-1 group-hover:rotate-0 transition-transform duration-300"></div>
        <div class="relative bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100">
          <!-- Quote icon -->
          <div class="absolute top-4 right-4 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z"/>
            </svg>
          </div>
          
          <div class="flex items-center mb-6">
            <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
              MD
            </div>
            <div class="ml-4">
              <div class="font-bold text-slate-900 text-lg">Mike Davis</div>
              <div class="text-green-600 font-medium">Startup Founder</div>
              <div class="text-sm text-slate-500">GrowthLab</div>
            </div>
          </div>
          
          <p class="text-slate-700 leading-relaxed mb-4 text-lg">"Finally! This is exactly what our industry has been waiting for. Excited to be part of this journey."</p>
          
          <div class="flex items-center justify-between">
            <div class="flex text-yellow-400 text-lg">
              ⭐⭐⭐⭐⭐
            </div>
            <div class="text-xs text-slate-400 font-medium">1 week ago</div>
          </div>
        </div>
      </div>
      
      <!-- Testimonial 3 -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-pink-600/5 rounded-2xl transform rotate-1 group-hover:rotate-0 transition-transform duration-300"></div>
        <div class="relative bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100">
          <!-- Quote icon -->
          <div class="absolute top-4 right-4 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z"/>
            </svg>
          </div>
          
          <div class="flex items-center mb-6">
            <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
              SJ
            </div>
            <div class="ml-4">
              <div class="font-bold text-slate-900 text-lg">Sarah Johnson</div>
              <div class="text-purple-600 font-medium">Design Director</div>
              <div class="text-sm text-slate-500">CreativeSpace</div>
            </div>
          </div>
          
          <p class="text-slate-700 leading-relaxed mb-4 text-lg">"The team behind this knows what they're doing. Clean design meets powerful functionality."</p>
          
          <div class="flex items-center justify-between">
            <div class="flex text-yellow-400 text-lg">
              ⭐⭐⭐⭐⭐
            </div>
            <div class="text-xs text-slate-400 font-medium">3 days ago</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Trust indicators -->
    <div class="text-center">
      <div class="inline-flex items-center space-x-8 px-8 py-4 bg-slate-50 rounded-2xl border border-slate-100">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span class="text-slate-600 font-medium">Live updates</span>
        </div>
        <div class="text-slate-300">•</div>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          <span class="text-slate-600 font-medium">Privacy protected</span>
        </div>
        <div class="text-slate-300">•</div>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <span class="text-slate-600 font-medium">Verified reviews</span>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Final CTA -->
<section class="relative py-32 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white overflow-hidden">
  <!-- Animated background -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-600/10 to-purple-600/10"></div>
    <div class="absolute top-10 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute bottom-10 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse animation-delay-2000"></div>
  </div>
  
  <!-- Grid overlay -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 50px 50px;"></div>
  </div>
  
  <div class="relative z-10 max-w-5xl mx-auto px-6 text-center">
    <!-- Urgency badge -->
    <div class="inline-flex items-center px-6 py-3 mb-8 bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-400/30 rounded-full text-orange-200 text-sm font-bold backdrop-blur-sm">
      <span class="w-2 h-2 bg-red-400 rounded-full mr-3 animate-ping"></span>
      ⏰ Early Bird Pricing Ends Soon • Limited Spots Remaining
    </div>
    
    <!-- Main headline -->
    <h2 class="text-5xl md:text-6xl font-bold mb-8 leading-tight">
      <span class="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
        Don't Miss Out!
      </span>
    </h2>
    
    <p class="text-2xl md:text-3xl text-blue-100 mb-6 font-light">
      Join <span class="font-bold text-white"><%= @waitlist_count %></span> visionary builders
    </p>
    
    <p class="text-xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
      Secure your spot in the future of innovation. Early access, founder pricing, and exclusive perks await.
    </p>
    
    <!-- Enhanced signup form -->
    <div class="max-w-2xl mx-auto mb-16">
      <%= form_with model: [@landing_page, WaitlistEntry.new], url: public_waitlist_entries_path(@landing_page), 
                    local: true, class: "space-y-8" do |form| %>
        
        <!-- Large form container -->
        <div class="relative p-8 bg-white/10 backdrop-blur-lg rounded-3xl border border-white/20 shadow-2xl">
          <div class="flex flex-col md:flex-row gap-6">
            <%= form.email_field :email, 
                  placeholder: "<EMAIL>", 
                  class: "flex-1 px-6 py-5 bg-white/95 backdrop-blur-sm rounded-2xl text-slate-900 placeholder-slate-500 border-0 focus:ring-4 focus:ring-blue-500/30 text-lg font-medium shadow-lg transition-all duration-300",
                  required: true %>
            
            <%= form.submit "🚀 Claim My Spot", 
                  class: "md:px-12 px-8 py-5 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold rounded-2xl transition-all duration-300 cursor-pointer shadow-lg hover:shadow-2xl transform hover:-translate-y-1 text-lg" %>
          </div>
          
          <!-- Benefits reminder -->
          <div class="grid md:grid-cols-3 gap-4 mt-6 text-sm">
            <div class="flex items-center justify-center text-blue-200">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              ⚡ Instant access when we launch
            </div>
            <div class="flex items-center justify-center text-green-200">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
              💰 50% off forever
            </div>
            <div class="flex items-center justify-center text-purple-200">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              🎁 Exclusive bonuses
            </div>
          </div>
        </div>
      <% end %>
      
      <!-- Trust signals -->
      <div class="flex items-center justify-center space-x-8 text-slate-400 text-sm">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          <span>100% secure</span>
        </div>
        <div class="text-slate-500">•</div>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
          <span>No spam ever</span>
        </div>
        <div class="text-slate-500">•</div>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
          </svg>
          <span>Unsubscribe anytime</span>
        </div>
      </div>
    </div>
    
    <!-- Final social proof -->
    <div class="text-center">
      <p class="text-slate-300 mb-4">Trusted by builders from:</p>
      <div class="flex items-center justify-center space-x-8 text-slate-400 text-sm font-medium">
        <span class="px-4 py-2 bg-white/5 rounded-lg">🏢 Fortune 500s</span>
        <span class="px-4 py-2 bg-white/5 rounded-lg">🚀 Startups</span>
        <span class="px-4 py-2 bg-white/5 rounded-lg">🎨 Agencies</span>
        <span class="px-4 py-2 bg-white/5 rounded-lg">👨‍💻 Freelancers</span>
      </div>
    </div>
  </div>
</section>

<!-- Flash Messages -->
<% if flash[:notice] %>
  <div class="fixed top-4 right-4 bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg z-50">
    <div class="flex items-center">
      <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
      </svg>
      <%= flash[:notice] %>
    </div>
  </div>
<% end %>

<% if flash[:alert] %>
  <div class="fixed top-4 right-4 bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg z-50">
    <div class="flex items-center">
      <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"/>
      </svg>
      <%= flash[:alert] %>
    </div>
  </div>
<% end %>

<!-- Custom Animations -->
<style>
  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }
  
  .animate-blob {
    animation: blob 7s infinite;
  }
  
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  
  .animation-delay-4000 {
    animation-delay: 4s;
  }
  
  /* Smooth scroll */
  html {
    scroll-behavior: smooth;
  }
  
  /* Glow effects */
  .group:hover .shadow-lg {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(59, 130, 246, 0.1);
  }
  
  /* Form focus effects */
  input:focus {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }
  
  /* Button hover effects */
  button:hover, input[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  /* Gradient animations */
  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
  
  .bg-gradient-animated {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
  }
  
  /* Floating animation */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  /* Loading states */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  /* Responsive improvements */
  @media (max-width: 640px) {
    .text-7xl {
      font-size: 3.5rem;
      line-height: 1.1;
    }
    
    .text-6xl {
      font-size: 3rem;
      line-height: 1.1;
    }
  }
</style>