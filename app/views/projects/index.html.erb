<!-- <PERSON> Header -->
<div class="bg-white shadow-sm border-b border-slate-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-slate-900">Dashboard</h1>
          <p class="text-sm text-slate-500 mt-1">Manage your projects and landing pages</p>
        </div>
        
        <%= link_to new_project_path, 
                    class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          New Project
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Dashboard Overview -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  
  <!-- Stats Grid -->
  <div class="grid md:grid-cols-4 gap-6 mb-8">
    <!-- Total Projects -->
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
      <div class="flex items-center">
        <div class="flex-1">
          <p class="text-sm font-medium text-slate-600">Projects</p>
          <p class="text-2xl font-bold text-slate-900 mt-1"><%= @stats[:total_projects] %></p>
        </div>
        <div class="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
        </div>
      </div>
    </div>
    
    <!-- Total Landing Pages -->
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
      <div class="flex items-center">
        <div class="flex-1">
          <p class="text-sm font-medium text-slate-600">Landing Pages</p>
          <p class="text-2xl font-bold text-slate-900 mt-1"><%= @stats[:total_landing_pages] %></p>
        </div>
        <div class="w-12 h-12 bg-indigo-50 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
        </div>
      </div>
    </div>
    
    <!-- Total Signups -->
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
      <div class="flex items-center">
        <div class="flex-1">
          <p class="text-sm font-medium text-slate-600">Total Signups</p>
          <p class="text-2xl font-bold text-green-600 mt-1"><%= @stats[:total_signups] %></p>
        </div>
        <div class="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
          </svg>
        </div>
      </div>
    </div>
    
    <!-- Conversion Rate -->
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
      <div class="flex items-center">
        <div class="flex-1">
          <p class="text-sm font-medium text-slate-600">Conversion Rate</p>
          <p class="text-2xl font-bold text-purple-600 mt-1"><%= @stats[:conversion_rate] %>%</p>
        </div>
        <div class="w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Projects List -->
  <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-slate-200">
      <h2 class="text-lg font-semibold text-slate-900">Your Projects</h2>
    </div>
    
    <% if @projects.any? %>
      <div class="divide-y divide-slate-200">
        <% @projects.each do |project| %>
          <div class="p-6 hover:bg-slate-50 transition-colors">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <% if project.logo_url.present? %>
                    <img src="<%= project.logo_url %>" alt="<%= project.name %>" class="w-8 h-8 rounded">
                  <% else %>
                    <span class="text-white font-bold text-lg"><%= project.name.first.upcase %></span>
                  <% end %>
                </div>
                <div>
                  <h3 class="text-lg font-medium text-slate-900">
                    <%= link_to project.name, project, class: "hover:text-blue-600 transition-colors" %>
                  </h3>
                  <% if project.description.present? %>
                    <p class="text-sm text-slate-500 mt-1"><%= truncate(project.description, length: 100) %></p>
                  <% end %>
                  <div class="flex items-center space-x-4 text-xs text-slate-400 mt-2">
                    <span><%= pluralize(project.landing_pages.count, 'landing page') %></span>
                    <span>•</span>
                    <% total_signups = ActsAsTenant.without_tenant { project.waitlist_entries.count } %>
                    <span><%= pluralize(total_signups, 'signup') %></span>
                    <span>•</span>
                    <span>Created <%= time_ago_in_words(project.created_at) %> ago</span>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <%= link_to "View", project, 
                            class: "inline-flex items-center px-3 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 transition-colors" %>
                <%= link_to "Edit", edit_project_path(project), 
                            class: "inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-slate-900">No projects yet</h3>
        <p class="mt-1 text-sm text-slate-500">Get started by creating your first project.</p>
        <div class="mt-6">
          <%= link_to new_project_path, 
                      class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            Create Project
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>