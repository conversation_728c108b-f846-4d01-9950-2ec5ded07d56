<!-- <PERSON> Header -->
<div class="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-8">
      <div class="flex items-start space-x-6">
        <%= link_to projects_path,
                    class: "inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors font-medium group" do %>
          <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Back to Projects
        <% end %>

        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-2">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </div>
            <h1 class="text-3xl font-bold text-slate-900">Create New Project</h1>
          </div>
          <p class="text-lg text-slate-600 max-w-2xl">Set up a new waitlist project to start collecting signups and building your audience before launch.</p>

          <!-- Progress indicator -->
          <div class="mt-6 flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
              <span class="text-sm font-medium text-blue-600">Project Details</span>
            </div>
            <div class="w-16 h-0.5 bg-slate-200"></div>
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-slate-200 text-slate-400 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
              <span class="text-sm text-slate-400">Customize & Launch</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Form -->
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
  <div class="bg-white rounded-xl sm:rounded-2xl shadow-lg sm:shadow-xl border border-slate-200 overflow-hidden">
    <!-- Form Header -->
    <div class="bg-gradient-to-r from-slate-50 to-slate-100 px-4 sm:px-8 py-4 sm:py-6 border-b border-slate-200">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg sm:text-xl font-bold text-slate-900">Project Information</h2>
          <p class="text-xs sm:text-sm text-slate-600 mt-1">Tell us about your project to get started</p>
        </div>
        <div class="text-xs text-slate-500 bg-white px-2 sm:px-3 py-1 rounded-full border">
          Step 1 of 2
        </div>
      </div>
    </div>

    <div class="p-4 sm:p-8">
      <%= form_with model: @project, local: true,
                    class: "space-y-8",
                    role: "form",
                    aria_labelledby: "form-title",
                    aria_describedby: "form-description",
                    data: {
                      controller: "form-validation",
                      action: "submit->form-validation#handleSubmit"
                    } do |form| %>

        <!-- Screen reader form description -->
        <div class="sr-only">
          <h2 id="form-title">Create New Project Form</h2>
          <p id="form-description">Fill out the following information to create a new waitlist project. Required fields are marked with an asterisk.</p>
        </div>
        <!-- Enhanced Error Display -->
        <% if @project.errors.any? %>
          <div class="bg-red-50 border-l-4 border-red-400 rounded-r-xl p-6 shadow-sm" role="alert">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-4 flex-1">
                <h3 class="text-lg font-semibold text-red-800 mb-2">
                  Please fix the following <%= pluralize(@project.errors.count, "issue") %>:
                </h3>
                <div class="space-y-2">
                  <% @project.errors.full_messages.each do |message| %>
                    <div class="flex items-center text-red-700">
                      <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                      </svg>
                      <%= message %>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Essential Information Section -->
        <fieldset class="space-y-6" aria-labelledby="essential-info-title" aria-describedby="essential-info-description">
          <legend class="sr-only">Essential Project Information</legend>
          <div class="border-l-4 border-blue-500 pl-6">
            <h3 id="essential-info-title" class="text-lg font-semibold text-slate-900 mb-1">Essential Information</h3>
            <p id="essential-info-description" class="text-sm text-slate-600 mb-6">Basic details to identify and describe your project</p>

            <!-- Project Name Field -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <%= form.label :name, class: "block text-sm font-semibold text-slate-900" %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Required
                  </span>
                </div>
                <button type="button"
                        class="text-slate-400 hover:text-slate-600 transition-colors"
                        data-controller="tooltip"
                        data-tooltip-content="Your project name will be used to create a unique URL and identify your waitlist. Choose something memorable and descriptive."
                        aria-label="Help for project name">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </button>
              </div>
              <div class="relative">
                <%= form.text_field :name,
                                    class: "w-full px-4 py-3 border-2 border-slate-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-lg font-medium placeholder-slate-400",
                                    placeholder: "e.g., TaskFlow Pro, EcoTracker, MindfulMoments",
                                    required: true,
                                    autocomplete: "off",
                                    aria_describedby: "name-help name-counter",
                                    data: {
                                      controller: "character-counter smart-suggestions",
                                      character_counter_max_value: 100,
                                      action: "input->character-counter#update input->smart-suggestions#generateSuggestions"
                                    } %>
                <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                  <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                  </svg>
                </div>
              </div>

              <!-- Smart suggestions -->
              <div class="hidden" data-smart-suggestions-target="container">
                <div class="text-xs text-slate-600 mb-2">💡 Suggestions based on your input:</div>
                <div class="flex flex-wrap gap-2" data-smart-suggestions-target="suggestions"></div>
              </div>

              <div class="flex justify-between items-center">
                <p id="name-help" class="text-sm text-slate-600">Choose a memorable name that represents your project</p>
                <span id="name-counter" class="text-xs text-slate-500" data-character-counter-target="counter" aria-live="polite">0/100</span>
              </div>
            </div>

            <!-- Project Description Field -->
            <div class="space-y-2 mt-6">
              <%= form.label :description, class: "block text-sm font-semibold text-slate-900" %>
              <div class="relative">
                <%= form.text_area :description,
                                   rows: 4,
                                   class: "w-full px-4 py-3 border-2 border-slate-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 placeholder-slate-400 resize-none",
                                   placeholder: "A brief description of what your project does and who it's for. This helps you organize multiple projects and provides context for your team.",
                                   aria_describedby: "description-help description-counter",
                                   data: {
                                     controller: "auto-resize character-counter",
                                     character_counter_max_value: 1000,
                                     action: "input->character-counter#update input->auto-resize#resize"
                                   } %>
                <div class="absolute bottom-3 right-3">
                  <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                  </svg>
                </div>
              </div>
              <div class="flex justify-between items-center">
                <p id="description-help" class="text-sm text-slate-600">Optional but recommended for project organization</p>
                <span id="description-counter" class="text-xs text-slate-500" data-character-counter-target="counter" aria-live="polite">0/1000</span>
              </div>
            </div>
          </div>
        </fieldset>

        <!-- Optional Settings Section -->
        <fieldset class="space-y-6" aria-labelledby="optional-settings-title" aria-describedby="optional-settings-description">
          <legend class="sr-only">Optional Project Settings</legend>
          <div class="border border-slate-200 rounded-xl p-6 bg-slate-50/50">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 id="optional-settings-title" class="text-lg font-semibold text-slate-900">Optional Settings</h3>
                <p id="optional-settings-description" class="text-sm text-slate-600">Additional configuration to customize your project</p>
              </div>
              <button type="button"
                      class="text-blue-600 hover:text-blue-800 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
                      data-controller="disclosure"
                      data-action="click->disclosure#toggle"
                      aria-expanded="false"
                      aria-controls="optional-settings-content"
                      aria-label="Toggle optional settings">
                <span data-disclosure-target="toggleText">Show Options</span>
                <svg class="w-4 h-4 ml-1 inline transform transition-transform" data-disclosure-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
            </div>

            <div id="optional-settings-content" class="space-y-6 hidden" data-disclosure-target="content">
              <!-- Website URL Field -->
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <%= form.label :website_url, "Website URL", class: "block text-sm font-semibold text-slate-900" %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-600">
                    Optional
                  </span>
                </div>
                <div class="relative">
                  <%= form.url_field :website_url,
                                     class: "w-full px-4 py-3 border-2 border-slate-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 placeholder-slate-400",
                                     placeholder: "https://myproject.com" %>
                  <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                    <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                  </div>
                </div>
                <p class="text-sm text-slate-600">Link to your main website or landing page</p>
              </div>

              <!-- Logo URL Field -->
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <%= form.label :logo_url, "Logo URL", class: "block text-sm font-semibold text-slate-900" %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-600">
                    Optional
                  </span>
                </div>
                <div class="relative">
                  <%= form.url_field :logo_url,
                                     class: "w-full px-4 py-3 border-2 border-slate-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 placeholder-slate-400",
                                     placeholder: "https://example.com/logo.png",
                                     data: {
                                       controller: "logo-preview",
                                       action: "input->logo-preview#updatePreview"
                                     } %>
                  <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                    <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <p class="text-sm text-slate-600">URL to your project logo image</p>
                  <div class="w-8 h-8 bg-slate-100 rounded border hidden" data-logo-preview-target="preview">
                    <img class="w-full h-full object-cover rounded" data-logo-preview-target="image" alt="Logo preview">
                  </div>
                </div>
              </div>

              <!-- Double Opt-in Setting -->
              <div class="bg-white rounded-lg p-4 border border-slate-200">
                <div class="flex items-start space-x-4">
                  <div class="flex items-center h-6 mt-1">
                    <%= form.check_box :double_opt_in,
                                       class: "w-5 h-5 text-blue-600 border-2 border-slate-300 rounded focus:ring-2 focus:ring-blue-500 transition-colors",
                                       checked: true %>
                  </div>
                  <div class="flex-1">
                    <%= form.label :double_opt_in, class: "text-sm font-semibold text-slate-900 cursor-pointer" do %>
                      Enable double opt-in email confirmation
                    <% end %>
                    <p class="text-sm text-slate-600 mt-1">Require users to confirm their email address before joining the waitlist. This improves email deliverability and reduces spam signups.</p>
                    <div class="mt-2 text-xs text-blue-600 bg-blue-50 px-3 py-1 rounded-full inline-block">
                      ✓ Recommended for better email quality
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="bg-slate-50 -mx-4 sm:-mx-8 -mb-4 sm:-mb-8 px-4 sm:px-8 py-4 sm:py-6 border-t border-slate-200">
          <div class="flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-0">
            <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
              <%= link_to projects_path,
                          class: "inline-flex items-center justify-center w-full sm:w-auto px-4 py-2 border-2 border-slate-300 rounded-xl text-sm font-medium text-slate-700 hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 group" do %>
                <svg class="w-4 h-4 mr-2 group-hover:-translate-x-0.5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Cancel
              <% end %>

              <button type="button"
                      class="text-sm text-slate-500 hover:text-slate-700 transition-colors w-full sm:w-auto text-center"
                      data-controller="form-saver"
                      data-action="click->form-saver#saveDraft">
                💾 Save as Draft
              </button>
            </div>

            <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
              <!-- Form validation summary -->
              <div class="hidden text-sm text-slate-600 order-2 sm:order-1" data-form-validation-target="summary">
                <span class="text-green-600">✓</span> Ready to create
              </div>

              <%= form.submit class: "inline-flex items-center justify-center w-full sm:w-auto px-6 sm:px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl text-sm font-semibold hover:from-blue-700 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none order-1 sm:order-2",
                              data: {
                                controller: "loading-button",
                                action: "click->loading-button#showLoading",
                                loading_button_text_value: "Creating Project..."
                              } do %>
                <svg class="w-5 h-5 mr-2 hidden" data-loading-button-target="spinner" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg class="w-5 h-5 mr-2" data-loading-button-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span data-loading-button-target="text">Create Project</span>
              <% end %>
            </div>
          </div>

          <!-- Help text -->
          <div class="mt-4 text-xs text-slate-500 text-center">
            By creating a project, you agree to our
            <a href="#" class="text-blue-600 hover:text-blue-800 underline">Terms of Service</a> and
            <a href="#" class="text-blue-600 hover:text-blue-800 underline">Privacy Policy</a>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>