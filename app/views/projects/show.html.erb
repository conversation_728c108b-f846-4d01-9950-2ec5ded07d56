<!-- <PERSON> Header -->
<div class="bg-white shadow-sm border-b border-slate-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to projects_path, 
                      class: "inline-flex items-center text-slate-500 hover:text-slate-700 transition-colors" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Projects
          <% end %>
          
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <% if @project.logo_url.present? %>
                <img src="<%= @project.logo_url %>" alt="<%= @project.name %>" class="w-8 h-8 rounded">
              <% else %>
                <span class="text-white font-bold text-lg"><%= @project.name.first.upcase %></span>
              <% end %>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-slate-900"><%= @project.name %></h1>
              <% if @project.description.present? %>
                <p class="text-sm text-slate-500 mt-1"><%= @project.description %></p>
              <% end %>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <%= link_to new_project_landing_page_path(@project), 
                      class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            New Landing Page
          <% end %>
          
          <%= link_to edit_project_path(@project), 
                      class: "inline-flex items-center px-4 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit Project
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="grid lg:grid-cols-3 gap-8">
    
    <!-- Main Column -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Project Analytics -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Project Analytics</h2>
        </div>
        <div class="p-6">
          <div class="grid md:grid-cols-2 gap-6 mb-6">
            <div class="text-center">
              <div class="text-3xl font-bold text-blue-600 mb-1"><%= @analytics[:total_landing_pages] %></div>
              <div class="text-sm text-slate-500">Landing Pages</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600 mb-1"><%= @analytics[:total_signups] %></div>
              <div class="text-sm text-slate-500">Total Signups</div>
            </div>
          </div>
          
          <div class="grid md:grid-cols-2 gap-6">
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600 mb-1"><%= @analytics[:confirmed_signups] %></div>
              <div class="text-sm text-slate-500">Confirmed</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-orange-600 mb-1"><%= @analytics[:conversion_rate] %>%</div>
              <div class="text-sm text-slate-500">Conversion Rate</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Landing Pages -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
          <h2 class="text-lg font-semibold text-slate-900">Landing Pages</h2>
          <% if @landing_pages.any? %>
            <%= link_to project_landing_pages_path(@project), 
                        class: "text-sm text-blue-600 hover:text-blue-700 font-medium" do %>
              View all <%= pluralize(@landing_pages.count, 'page') %> →
            <% end %>
          <% end %>
        </div>
        <div class="p-6">
          <% if @landing_pages.any? %>
            <div class="space-y-4">
              <% @landing_pages.each do |landing_page| %>
                <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors">
                  <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center">
                      <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                    </div>
                    <div>
                      <h3 class="font-medium text-slate-900">
                        <%= link_to landing_page.title, project_landing_page_path(@project, landing_page), 
                                    class: "hover:text-blue-600 transition-colors" %>
                      </h3>
                      <div class="flex items-center space-x-4 text-sm text-slate-500 mt-1">
                        <span class="flex items-center">
                          <div class="w-2 h-2 <%= landing_page.published? ? 'bg-green-400' : 'bg-yellow-400' %> rounded-full mr-2"></div>
                          <%= landing_page.published? ? 'Published' : 'Draft' %>
                        </span>
                        <span>•</span>
                        <span><%= pluralize(landing_page.waitlist_entries.count, 'signup') %></span>
                        <span>•</span>
                        <span><%= pluralize(landing_page.page_views, 'view') %></span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-2">
                    <% if landing_page.published? %>
                      <%= link_to "Preview", preview_project_landing_page_path(@project, landing_page), 
                                  target: "_blank",
                                  class: "text-sm text-slate-500 hover:text-slate-700" %>
                    <% end %>
                    <%= link_to "Manage", project_landing_page_path(@project, landing_page), 
                                class: "text-sm text-blue-600 hover:text-blue-700 font-medium" %>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <h3 class="text-lg font-medium text-slate-900 mb-2">No landing pages yet</h3>
              <p class="text-slate-500 mb-4">Create your first landing page to start collecting signups.</p>
              <%= link_to new_project_landing_page_path(@project), 
                          class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors" do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Create Landing Page
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    
    <!-- Sidebar -->
    <div class="space-y-6">
      
      <!-- Recent Signups -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Recent Signups</h2>
        </div>
        <div class="p-6">
          <% if @analytics[:recent_signups].any? %>
            <div class="space-y-4">
              <% @analytics[:recent_signups].each do |entry| %>
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-medium">
                      <%= entry.email.first.upcase %>
                    </span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-slate-900 truncate"><%= entry.email %></div>
                    <div class="text-xs text-slate-500"><%= time_ago_in_words(entry.created_at) %> ago</div>
                  </div>
                  <% if entry.confirmed? %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Confirmed
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-6">
              <svg class="w-8 h-8 text-slate-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              <p class="text-sm text-slate-500">No signups yet</p>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Project Settings -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Project Settings</h2>
        </div>
        <div class="p-6 space-y-4">
          <div>
            <label class="text-sm font-medium text-slate-700">Website URL</label>
            <p class="text-sm text-slate-900 mt-1">
              <%= @project.website_url.present? ? link_to(@project.website_url, @project.website_url, target: "_blank", class: "text-blue-600 hover:text-blue-700") : '-' %>
            </p>
          </div>
          
          <div>
            <label class="text-sm font-medium text-slate-700">Double Opt-in</label>
            <p class="text-sm text-slate-900 mt-1">
              <% if @project.double_opt_in? %>
                <span class="text-green-600">✓ Enabled</span>
              <% else %>
                <span class="text-slate-500">Disabled</span>
              <% end %>
            </p>
          </div>
          
          <div class="pt-4 border-t border-slate-200">
            <p class="text-xs text-slate-500">
              Created <%= time_ago_in_words(@project.created_at) %> ago
            </p>
          </div>
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Quick Actions</h2>
        </div>
        <div class="p-6 space-y-3">
          <%= link_to new_project_landing_page_path(@project), 
                      class: "flex items-center w-full px-4 py-3 text-left rounded-lg hover:bg-slate-50 transition-colors border border-slate-200" do %>
            <svg class="w-5 h-5 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <span class="font-medium text-slate-900">Create Landing Page</span>
          <% end %>
          
          <%= link_to edit_project_path(@project), 
                      class: "flex items-center w-full px-4 py-3 text-left rounded-lg hover:bg-slate-50 transition-colors border border-slate-200" do %>
            <svg class="w-5 h-5 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            <span class="font-medium text-slate-900">Edit Project</span>
          <% end %>
          
          <%= link_to @project, 
                      method: :delete,
                      confirm: "Are you sure you want to delete this project? This will also delete all landing pages and waitlist entries.",
                      class: "flex items-center w-full px-4 py-3 text-left rounded-lg hover:bg-red-50 transition-colors border border-red-200 text-red-600" do %>
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            <span class="font-medium">Delete Project</span>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>