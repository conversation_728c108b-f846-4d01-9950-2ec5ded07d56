<!-- <PERSON> Header -->
<div class="bg-white shadow-sm border-b border-slate-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
      <div class="flex items-center space-x-4">
        <%= link_to @project, 
                    class: "inline-flex items-center text-slate-500 hover:text-slate-700 transition-colors" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Back to Project
        <% end %>
        
        <div>
          <h1 class="text-2xl font-bold text-slate-900">Edit Project</h1>
          <p class="text-sm text-slate-500 mt-1">Update your project settings</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Form -->
<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-slate-200">
      <h2 class="text-lg font-semibold text-slate-900">Project Details</h2>
    </div>
    
    <div class="p-6">
      <%= form_with model: @project, local: true, class: "space-y-6" do |form| %>
        <% if @project.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <h3 class="text-sm font-medium text-red-800">
                  <%= pluralize(@project.errors.count, "error") %> prohibited this project from being saved:
                </h3>
                <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                  <% @project.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        <% end %>

        <div>
          <%= form.label :name, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_field :name, 
                              class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                              placeholder: "My Awesome Project" %>
          <p class="mt-1 text-sm text-slate-500">Choose a descriptive name for your project</p>
        </div>

        <div>
          <%= form.label :description, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_area :description, 
                             rows: 4,
                             class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                             placeholder: "Brief description of your project..." %>
          <p class="mt-1 text-sm text-slate-500">Optional description to help you identify this project</p>
        </div>

        <div>
          <%= form.label :website_url, "Website URL", class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.url_field :website_url, 
                             class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                             placeholder: "https://myproject.com" %>
          <p class="mt-1 text-sm text-slate-500">Your main website (optional)</p>
        </div>

        <div>
          <%= form.label :logo_url, "Logo URL", class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.url_field :logo_url, 
                             class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500",
                             placeholder: "https://example.com/logo.png" %>
          <p class="mt-1 text-sm text-slate-500">URL to your project logo (optional)</p>
        </div>

        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :double_opt_in, 
                               class: "w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500" %>
          </div>
          <div class="ml-3">
            <%= form.label :double_opt_in, class: "text-sm font-medium text-slate-700" do %>
              Enable double opt-in
            <% end %>
            <p class="text-sm text-slate-500">Require users to confirm their email address before joining the waitlist</p>
          </div>
        </div>

        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-slate-200">
          <%= link_to "Cancel", @project, 
                      class: "px-4 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 hover:bg-slate-50 transition-colors" %>
          <%= form.submit "Update Project", 
                          class: "px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>
</div>