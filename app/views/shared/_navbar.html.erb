<nav class="bg-white/90 backdrop-blur-md shadow-sm border-b border-slate-200/60 sticky top-0 z-50" data-controller="mobile-menu">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-20 items-center">
      
      <!-- Logo Section -->
      <div class="flex items-center space-x-2">
        <%= link_to root_path, class: "group flex items-center space-x-3" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg flex items-center justify-center group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
            </svg>
          </div>
          <div class="hidden sm:block">
            <h1 class="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              WaitlistBuilder
            </h1>
            <p class="text-xs text-slate-500 -mt-1">Create beautiful waitlists</p>
          </div>
        <% end %>
      </div>

      <!-- Desktop Navigation Links (Hidden on Mobile) -->
      <div class="hidden lg:flex lg:items-center lg:space-x-8">
        <% unless user_signed_in? %>
          <!-- Public Navigation Links -->
          <div class="flex items-center space-x-6">
            <a href="#features" class="text-slate-600 hover:text-slate-900 font-medium transition-colors duration-200">
              Features
            </a>
            <a href="#pricing" class="text-slate-600 hover:text-slate-900 font-medium transition-colors duration-200">
              Pricing
            </a>
            <a href="#examples" class="text-slate-600 hover:text-slate-900 font-medium transition-colors duration-200">
              Examples
            </a>
            <a href="#support" class="text-slate-600 hover:text-slate-900 font-medium transition-colors duration-200">
              Support
            </a>
          </div>
        <% else %>
          <!-- Authenticated Navigation Links -->
          <div class="flex items-center space-x-6">
            <%= link_to projects_path, class: "flex items-center text-slate-600 hover:text-slate-900 font-medium transition-colors duration-200" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              Dashboard
            <% end %>
            <%= link_to new_project_path, class: "flex items-center text-slate-600 hover:text-slate-900 font-medium transition-colors duration-200" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              New Project
            <% end %>
          </div>
        <% end %>
      </div>
      
      <!-- Right Side: Auth + Mobile Menu Button -->
      <div class="flex items-center space-x-4">
        
        <!-- Desktop Authentication Section -->
        <div class="hidden lg:flex lg:items-center lg:space-x-4">
          <% if user_signed_in? %>
            <!-- User Account Info -->
            <div class="relative group">
              <button class="flex items-center space-x-3 bg-white/60 rounded-full px-4 py-2 shadow-sm border border-slate-200/60 hover:bg-white/80 transition-all duration-200">
                <div class="w-8 h-8 bg-gradient-to-br from-emerald-400 to-blue-500 rounded-full flex items-center justify-center shadow-sm">
                  <span class="text-white text-sm font-medium">
                    <%= current_user.first_name&.first&.upcase || current_user.email.first.upcase %>
                  </span>
                </div>
                <div class="text-sm text-left">
                  <p class="font-medium text-slate-900">
                    <%= current_user.display_name %>
                  </p>
                  <% if current_account %>
                    <p class="text-slate-500 text-xs"><%= current_account.name %></p>
                  <% end %>
                </div>
                <svg class="w-4 h-4 text-slate-400 group-hover:text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
              
              <!-- Dropdown Menu -->
              <div class="absolute right-0 mt-2 w-72 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-slate-200/60 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform translate-y-1 group-hover:translate-y-0">
                <div class="p-4 border-b border-slate-100">
                  <p class="font-medium text-slate-900"><%= current_user.display_name %></p>
                  <p class="text-sm text-slate-500"><%= current_user.email %></p>
                  <% if current_account %>
                    <div class="mt-2 inline-flex items-center px-2 py-1 bg-slate-100 rounded-full text-xs text-slate-600">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                      </svg>
                      <%= current_account.name %>
                    </div>
                  <% end %>
                </div>
                
                <div class="p-2">
                  <a href="#profile" class="flex items-center px-3 py-2 text-sm text-slate-700 hover:bg-slate-50 rounded-lg transition-colors duration-150">
                    <svg class="w-4 h-4 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Profile Settings
                  </a>
                  <a href="#account" class="flex items-center px-3 py-2 text-sm text-slate-700 hover:bg-slate-50 rounded-lg transition-colors duration-150">
                    <svg class="w-4 h-4 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Account Settings
                  </a>
                  <a href="#billing" class="flex items-center px-3 py-2 text-sm text-slate-700 hover:bg-slate-50 rounded-lg transition-colors duration-150">
                    <svg class="w-4 h-4 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                    Billing
                  </a>
                  <div class="border-t border-slate-100 my-2"></div>
                  <a href="#support" class="flex items-center px-3 py-2 text-sm text-slate-700 hover:bg-slate-50 rounded-lg transition-colors duration-150">
                    <svg class="w-4 h-4 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                    Help & Support
                  </a>
                  <%= link_to destroy_user_session_path, method: :delete, 
                              class: "flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-150" do %>
                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    Sign out
                  <% end %>
                </div>
              </div>
            </div>
          <% else %>
            <!-- Guest User Actions -->
            <div class="flex items-center space-x-3">
              <%= link_to "Sign in", new_user_session_path, 
                          class: "inline-flex items-center px-4 py-2 text-sm font-medium text-slate-700 hover:text-slate-900 hover:bg-white/80 rounded-lg transition-all duration-200" %>
              <%= link_to "Get Started", new_user_registration_path, 
                          class: "inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-sm font-medium rounded-lg shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 transform hover:-translate-y-0.5" %>
            </div>
          <% end %>
        </div>

        <!-- Mobile Menu Button -->
        <button 
          data-mobile-menu-target="button"
          data-action="click->mobile-menu#toggle"
          class="lg:hidden p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100 transition-colors duration-200 relative" 
          type="button"
          aria-expanded="false"
          aria-label="Toggle navigation menu">
          
          <!-- Hamburger Icon -->
          <svg data-mobile-menu-target="hamburger" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          
          <!-- Close Icon (Hidden by default) -->
          <svg data-mobile-menu-target="close" class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div 
      data-mobile-menu-target="menu" 
      class="lg:hidden border-t border-slate-200/60 py-4 hidden"
      aria-hidden="true">
      
      <div class="space-y-4">
        <% unless user_signed_in? %>
          <!-- Public Mobile Links -->
          <div class="space-y-2">
            <a href="#features" 
               data-action="click->mobile-menu#closeMenu"
               class="block px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200 font-medium">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Features
              </div>
            </a>
            <a href="#pricing" 
               data-action="click->mobile-menu#closeMenu"
               class="block px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200 font-medium">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                Pricing
              </div>
            </a>
            <a href="#examples" 
               data-action="click->mobile-menu#closeMenu"
               class="block px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200 font-medium">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v1a2 2 0 002 2h4a2 2 0 012 2v8a4 4 0 01-4 4H7z"></path>
                </svg>
                Examples
              </div>
            </a>
            <a href="#support" 
               data-action="click->mobile-menu#closeMenu"
               class="block px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200 font-medium">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
                Support
              </div>
            </a>
          </div>
          
          <!-- Mobile Auth Actions -->
          <div class="border-t border-slate-200/60 pt-4 space-y-3">
            <%= link_to "Sign in", new_user_session_path, 
                        data: { action: "click->mobile-menu#closeMenu" },
                        class: "block px-4 py-3 text-center text-slate-700 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200 font-medium border border-slate-200" %>
            <%= link_to "Get Started", new_user_registration_path, 
                        data: { action: "click->mobile-menu#closeMenu" },
                        class: "block px-4 py-3 text-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium" %>
          </div>
        <% else %>
          <!-- Authenticated Mobile User Info -->
          <div class="px-4 py-3 bg-slate-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gradient-to-br from-emerald-400 to-blue-500 rounded-full flex items-center justify-center shadow-sm">
                <span class="text-white text-sm font-medium">
                  <%= current_user.first_name&.first&.upcase || current_user.email.first.upcase %>
                </span>
              </div>
              <div class="flex-1">
                <p class="font-medium text-slate-900">
                  <%= current_user.display_name %>
                </p>
                <p class="text-sm text-slate-500"><%= current_user.email %></p>
                <% if current_account %>
                  <div class="mt-1 inline-flex items-center px-2 py-1 bg-slate-200 rounded-full text-xs text-slate-600">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <%= current_account.name %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Authenticated Mobile Links -->
          <div class="space-y-2">
            <%= link_to projects_path,
                        data: { action: "click->mobile-menu#closeMenu" },
                        class: "flex items-center px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200 font-medium" do %>
              <svg class="w-5 h-5 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              Dashboard
            <% end %>
            <%= link_to new_project_path,
                        data: { action: "click->mobile-menu#closeMenu" },
                        class: "flex items-center px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200 font-medium" do %>
              <svg class="w-5 h-5 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              New Project
            <% end %>
          </div>
          
          <!-- Mobile Account Actions -->
          <div class="border-t border-slate-200/60 pt-4 space-y-2">
            <a href="#profile" 
               data-action="click->mobile-menu#closeMenu"
               class="flex items-center px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200">
              <svg class="w-5 h-5 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              Profile Settings
            </a>
            <a href="#account" 
               data-action="click->mobile-menu#closeMenu"
               class="flex items-center px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200">
              <svg class="w-5 h-5 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
              Account Settings
            </a>
            <%= link_to destroy_user_session_path, method: :delete, 
                        data: { action: "click->mobile-menu#closeMenu" },
                        class: "flex items-center px-4 py-3 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              Sign out
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</nav>