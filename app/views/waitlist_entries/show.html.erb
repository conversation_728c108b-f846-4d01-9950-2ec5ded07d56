<!-- <PERSON> Header -->
<div class="bg-white shadow-sm border-b border-slate-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to project_landing_page_waitlist_entries_path(@project, @landing_page), 
                      class: "inline-flex items-center text-slate-500 hover:text-slate-700 transition-colors" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Waitlist
          <% end %>
          
          <div>
            <h1 class="text-2xl font-bold text-slate-900">Waitlist Entry Details</h1>
            <p class="text-sm text-slate-500 mt-1"><%= @waitlist_entry.email %></p>
          </div>
        </div>
        
        <!-- Delete Button -->
        <%= link_to project_landing_page_waitlist_entry_path(@project, @landing_page, @waitlist_entry),
                    method: :delete,
                    data: { confirm: 'Are you sure you want to delete this entry?' },
                    class: "inline-flex items-center px-4 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
          </svg>
          Delete Entry
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="grid lg:grid-cols-3 gap-8">
    
    <!-- Main Column -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Contact Information -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Contact Information</h2>
        </div>
        <div class="p-6 space-y-4">
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <label class="text-sm font-medium text-slate-700">Email</label>
              <p class="mt-1 text-sm text-slate-900"><%= @waitlist_entry.email %></p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">Name</label>
              <p class="mt-1 text-sm text-slate-900">
                <%= [@waitlist_entry.first_name, @waitlist_entry.last_name].compact.join(' ').presence || '-' %>
              </p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">Company</label>
              <p class="mt-1 text-sm text-slate-900"><%= @waitlist_entry.company || '-' %></p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">Phone</label>
              <p class="mt-1 text-sm text-slate-900"><%= @waitlist_entry.phone || '-' %></p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Status & Engagement -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Status & Engagement</h2>
        </div>
        <div class="p-6 space-y-4">
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <label class="text-sm font-medium text-slate-700">Status</label>
              <p class="mt-1">
                <% case @waitlist_entry.status %>
                <% when 'confirmed' %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Confirmed
                  </span>
                <% when 'pending' %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Pending
                  </span>
                <% when 'rejected' %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    Rejected
                  </span>
                <% end %>
              </p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">Position in Queue</label>
              <p class="mt-1 text-sm text-slate-900">#<%= @waitlist_entry.position || '-' %></p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">Email Verified</label>
              <p class="mt-1">
                <% if @waitlist_entry.email_verified? %>
                  <span class="text-green-600">✓ Verified</span>
                <% else %>
                  <span class="text-slate-500">Not verified</span>
                <% end %>
              </p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">Estimated Wait Time</label>
              <p class="mt-1 text-sm text-slate-900"><%= @waitlist_entry.estimated_wait_time || '-' %></p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Technical Details -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Technical Details</h2>
        </div>
        <div class="p-6 space-y-4">
          <div class="space-y-3">
            <div>
              <label class="text-sm font-medium text-slate-700">IP Address</label>
              <p class="mt-1 text-sm text-slate-900 font-mono"><%= @waitlist_entry.ip_address || '-' %></p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">User Agent</label>
              <p class="mt-1 text-sm text-slate-900 break-all"><%= @waitlist_entry.user_agent || '-' %></p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">Referrer URL</label>
              <p class="mt-1 text-sm text-slate-900 break-all"><%= @waitlist_entry.referrer || '-' %></p>
            </div>
            
            <div>
              <label class="text-sm font-medium text-slate-700">Source</label>
              <p class="mt-1 text-sm text-slate-900"><%= @waitlist_entry.source_display %></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Sidebar -->
    <div class="space-y-6">
      
      <!-- Referral Information -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Referral Information</h2>
        </div>
        <div class="p-6 space-y-4">
          <div>
            <label class="text-sm font-medium text-slate-700">Referral Code</label>
            <div class="mt-1 flex items-center space-x-2">
              <code class="text-sm font-mono bg-slate-100 px-2 py-1 rounded"><%= @waitlist_entry.referral_code %></code>
              <button onclick="navigator.clipboard.writeText('<%= @waitlist_entry.referral_url %>')" 
                      class="text-sm text-blue-600 hover:text-blue-700">
                Copy Link
              </button>
            </div>
          </div>
          
          <div>
            <label class="text-sm font-medium text-slate-700">Referrals Made</label>
            <p class="mt-1 text-2xl font-bold text-slate-900"><%= @waitlist_entry.referrals_count %></p>
          </div>
          
          <div>
            <label class="text-sm font-medium text-slate-700">Bonus Positions</label>
            <p class="mt-1 text-sm text-slate-900"><%= @waitlist_entry.bonus_positions %> positions</p>
            <p class="text-xs text-slate-500 mt-1">1 position for every 3 referrals</p>
          </div>
          
          <% if @waitlist_entry.referred_by.present? %>
            <div>
              <label class="text-sm font-medium text-slate-700">Referred By</label>
              <p class="mt-1 text-sm text-slate-900"><%= @waitlist_entry.referred_by %></p>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Timeline -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="text-lg font-semibold text-slate-900">Timeline</h2>
        </div>
        <div class="p-6 space-y-4">
          <div class="space-y-3">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                  </svg>
                </div>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-slate-900">Joined Waitlist</p>
                <p class="text-sm text-slate-500"><%= @waitlist_entry.created_at.strftime('%B %d, %Y at %I:%M %p') %></p>
              </div>
            </div>
            
            <% if @waitlist_entry.confirmation_sent_at.present? %>
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-slate-900">Confirmation Email Sent</p>
                  <p class="text-sm text-slate-500"><%= @waitlist_entry.confirmation_sent_at.strftime('%B %d, %Y at %I:%M %p') %></p>
                </div>
              </div>
            <% end %>
            
            <% if @waitlist_entry.confirmed_at.present? %>
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-slate-900">Email Confirmed</p>
                  <p class="text-sm text-slate-500"><%= @waitlist_entry.confirmed_at.strftime('%B %d, %Y at %I:%M %p') %></p>
                </div>
              </div>
            <% end %>
            
            <% if @waitlist_entry.notified_at.present? %>
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-slate-900">Launch Notification Sent</p>
                  <p class="text-sm text-slate-500"><%= @waitlist_entry.notified_at.strftime('%B %d, %Y at %I:%M %p') %></p>
                </div>
              </div>
            <% end %>
          </div>
          
          <div class="pt-4 border-t border-slate-200">
            <p class="text-sm text-slate-500">
              <span class="font-medium">Days waiting:</span> <%= @waitlist_entry.days_waiting.round %> days
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>