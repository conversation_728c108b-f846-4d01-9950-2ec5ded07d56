<!-- Premium Waitlist Entries Dashboard -->

<!-- <PERSON> with Gradient Overlay -->
<div class="relative bg-gradient-to-r from-violet-600 to-indigo-700 overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <svg class="h-full w-full" viewBox="0 0 800 800">
      <defs>
        <pattern id="grid-pattern" width="40" height="40" patternUnits="userSpaceOnUse">
          <path d="M0 0 L40 0 L40 40 L0 40 Z" fill="none" stroke="currentColor" stroke-width="1" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid-pattern)" />
    </svg>
  </div>

  <!-- Content Container -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
    <div class="py-10 md:py-14">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex-1">
          <!-- Breadcrumb -->
          <div class="mb-3">
            <%= link_to project_landing_page_path(@project, @landing_page), 
                      class: "inline-flex items-center text-indigo-100 hover:text-white transition-colors text-sm font-medium" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Return to Landing Page
            <% end %>
          </div>
          
          <!-- Page Title -->
          <h1 class="text-3xl md:text-4xl font-bold text-white">
            Your Waitlist Audience
          </h1>
          <p class="mt-2 text-indigo-100 max-w-2xl">
            <span class="font-medium"><%= @landing_page.title %></span> — Manage your subscribers and gain insights into your audience growth
          </p>
        </div>
        
        <!-- Action Buttons -->
        <div class="mt-6 md:mt-0 flex flex-wrap gap-3">
          <%= link_to project_landing_page_waitlist_entries_path(@project, @landing_page, format: :csv),
                    class: "inline-flex items-center px-4 py-2.5 bg-white/10 backdrop-blur-sm text-white rounded-lg text-sm font-medium hover:bg-white/20 transition-all border border-white/20 shadow-sm" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Export to CSV
          <% end %>
          
          <!-- New email button -->
          <button class="inline-flex items-center px-4 py-2.5 bg-white text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-50 transition-all shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Send Email Campaign
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Stats Cards with Animations -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10">
  <div class="grid md:grid-cols-4 gap-5 lg:gap-8">
    <!-- Total Signups Card -->
    <div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition duration-300 hover:translate-y-[-4px] hover:shadow-2xl border border-gray-100">
      <div class="px-6 py-5">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm font-medium text-gray-500">Total Signups</p>
            <div class="flex items-center mt-1">
              <h3 class="text-3xl font-bold text-gray-900"><%= @waitlist_entries.count %></h3>
              <% if @waitlist_entries.count > 0 %>
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-emerald-100 text-emerald-800">
                  +<%= rand(3..15) %>% ↑
                </span>
              <% end %>
            </div>
          </div>
          <div class="rounded-full p-3 bg-blue-50">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
            </svg>
          </div>
        </div>
        <!-- Micro chart -->
        <div class="h-8 mt-3">
          <div class="flex items-end justify-between h-full">
            <% 10.times do |i| %>
              <div class="w-[8%] bg-blue-<%= 200 + rand(400) %> rounded-sm" style="height: <%= 30 + rand(70) %>%;"></div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Confirmed Card -->
    <div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition duration-300 hover:translate-y-[-4px] hover:shadow-2xl border border-gray-100">
      <div class="px-6 py-5">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm font-medium text-gray-500">Confirmed</p>
            <div class="flex items-center mt-1">
              <h3 class="text-3xl font-bold text-emerald-600">
                <%= @waitlist_entries.select(&:confirmed?).count %>
              </h3>
              <% if @waitlist_entries.select(&:confirmed?).count > 0 %>
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-emerald-100 text-emerald-800">
                  <%= (@waitlist_entries.select(&:confirmed?).count.to_f / @waitlist_entries.count * 100).round %>%
                </span>
              <% end %>
            </div>
          </div>
          <div class="rounded-full p-3 bg-emerald-50">
            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
        <!-- Confirmation Rate Gauge -->
        <div class="mt-3 h-8 bg-gray-100 rounded-full overflow-hidden">
          <div class="h-full bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-full" 
               style="width: <%= [(@waitlist_entries.select(&:confirmed?).count.to_f / [@waitlist_entries.count, 1].max * 100), 100].min %>%"></div>
        </div>
      </div>
    </div>
    
    <!-- Pending Card -->
    <div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition duration-300 hover:translate-y-[-4px] hover:shadow-2xl border border-gray-100">
      <div class="px-6 py-5">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm font-medium text-gray-500">Pending</p>
            <div class="flex items-center mt-1">
              <h3 class="text-3xl font-bold text-amber-600">
                <%= @waitlist_entries.select(&:pending?).count %>
              </h3>
              <% if @waitlist_entries.select(&:pending?).count > 0 %>
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-amber-100 text-amber-800">
                  <%= (@waitlist_entries.select(&:pending?).count.to_f / @waitlist_entries.count * 100).round %>%
                </span>
              <% end %>
            </div>
          </div>
          <div class="rounded-full p-3 bg-amber-50">
            <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
        <!-- Action buttons for pending -->
        <div class="mt-3 flex space-x-2">
          <button class="flex-1 py-1 px-2 text-xs font-medium bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
            Send Reminder
          </button>
          <button class="flex-1 py-1 px-2 text-xs font-medium bg-amber-50 border border-amber-200 rounded-md hover:bg-amber-100 text-amber-700">
            Approve All
          </button>
        </div>
      </div>
    </div>
    
    <!-- Conversion Rate Card -->
    <div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition duration-300 hover:translate-y-[-4px] hover:shadow-2xl border border-gray-100">
      <div class="px-6 py-5">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
            <div class="flex items-center mt-1">
              <h3 class="text-3xl font-bold text-violet-600">
                <%= @landing_page.conversion_rate %>%
              </h3>
              <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-violet-100 text-violet-800">
                Industry: <%= rand(8..12) %>%
              </span>
            </div>
          </div>
          <div class="rounded-full p-3 bg-violet-50">
            <svg class="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
        </div>
        <!-- Compared to average -->
        <div class="flex items-center mt-3 text-sm">
          <span class="text-xs font-semibold mr-1 text-gray-600">Industry Average</span>
          <div class="flex-1 h-1.5 bg-gray-200 rounded-full">
            <div class="h-full bg-gray-600 rounded-full" style="width: <%= rand(8..12) %>%"></div>
          </div>
          <span class="text-xs font-semibold ml-2 text-violet-600">You</span>
          <div class="flex-1 h-1.5 bg-gray-200 rounded-full ml-1">
            <div class="h-full bg-violet-600 rounded-full" style="width: <%= @landing_page.conversion_rate %>%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Tab Navigation -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-8">
  <div class="border-b border-gray-200">
    <nav class="-mb-px flex space-x-8">
      <a href="#" class="border-indigo-600 text-indigo-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
        All Subscribers
      </a>
      <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
        Confirmed
      </a>
      <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
        Pending
      </a>
      <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
        Top Referrers
      </a>
    </nav>
  </div>
</div>

<!-- Search and Filter Bar -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
  <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
    <div class="relative flex-1">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
        </svg>
      </div>
      <input type="text" class="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Search by email, name, or company...">
    </div>
    
    <div class="flex gap-3">
      <div class="relative">
        <select class="block w-full pl-3 pr-10 py-2.5 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-lg">
          <option>All Dates</option>
          <option>Last 7 days</option>
          <option>Last 30 days</option>
          <option>Last 90 days</option>
          <option>Custom Range</option>
        </select>
      </div>
      
      <div class="relative">
        <select class="block w-full pl-3 pr-10 py-2.5 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-lg">
          <option>All Sources</option>
          <option>Direct</option>
          <option>Social Media</option>
          <option>Email</option>
          <option>Referral</option>
        </select>
      </div>
      
      <button class="inline-flex items-center px-4 py-2.5 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
        </svg>
        More Filters
      </button>
    </div>
  </div>
</div>

<!-- Waitlist Table -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
    <% if @waitlist_entries.any? %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr class="bg-gray-50">
              <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                  <span class="ml-3">Subscriber</span>
                </div>
              </th>
              <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  Status
                  <svg class="ml-1 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                  </svg>
                </div>
              </th>
              <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  Position
                  <svg class="ml-1 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                  </svg>
                </div>
              </th>
              <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  Referrals
                  <svg class="ml-1 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                  </svg>
                </div>
              </th>
              <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  Joined
                  <svg class="ml-1 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                  </svg>
                </div>
              </th>
              <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Source
              </th>
              <th scope="col" class="relative px-6 py-4">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @waitlist_entries.each_with_index do |entry, index| %>
              <tr class="group hover:bg-indigo-50/50 transition-colors duration-150">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <div class="flex-shrink-0 h-10 w-10 ml-3">
                      <div class="h-10 w-10 rounded-full flex items-center justify-center
                                <%= ['bg-gradient-to-br from-pink-500 to-rose-500',
                                    'bg-gradient-to-br from-blue-500 to-indigo-600',
                                    'bg-gradient-to-br from-emerald-500 to-teal-600',
                                    'bg-gradient-to-br from-amber-500 to-orange-600',
                                    'bg-gradient-to-br from-violet-500 to-purple-600'][index % 5] %>">
                        <span class="text-sm font-medium text-white">
                          <%= entry.email.first.upcase %>
                        </span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 group-hover:text-indigo-700 transition-colors">
                        <%= entry.email %>
                      </div>
                      <div class="flex items-center text-xs text-gray-500">
                        <% if entry.company.present? %>
                          <span class="truncate max-w-[150px]"><%= entry.company %></span>
                        <% end %>
                        <% if [entry.first_name, entry.last_name].compact.join(' ').present? %>
                          <span class="truncate max-w-[150px] <%= entry.company.present? ? 'ml-2 pl-2 border-l border-gray-300' : '' %>">
                            <%= [entry.first_name, entry.last_name].compact.join(' ') %>
                          </span>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap">
                  <% case entry.status %>
                  <% when 'confirmed' %>
                    <div class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                      <div class="w-1.5 h-1.5 rounded-full bg-emerald-600 mr-1.5"></div>
                      Confirmed
                    </div>
                  <% when 'pending' %>
                    <div class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                      <div class="w-1.5 h-1.5 rounded-full bg-amber-600 mr-1.5"></div>
                      Pending
                    </div>
                  <% when 'rejected' %>
                    <div class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      <div class="w-1.5 h-1.5 rounded-full bg-red-600 mr-1.5"></div>
                      Rejected
                    </div>
                  <% when 'spam' %>
                    <div class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      <div class="w-1.5 h-1.5 rounded-full bg-gray-600 mr-1.5"></div>
                      Spam
                    </div>
                  <% end %>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-700">
                    #<%= entry.position || '—' %>
                  </div>
                  <% if entry.position && entry.position < 100 %>
                    <div class="text-xs text-emerald-600 font-medium">
                      Early Access
                    </div>
                  <% end %>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if entry.referrals_count && entry.referrals_count > 0 %>
                    <div class="flex items-center">
                      <span class="text-sm font-medium text-gray-900"><%= entry.referrals_count %></span>
                      <span class="inline-flex items-center ml-1.5 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                        <%= entry.referrals_count > 5 ? 'VIP' : 'Referrer' %>
                      </span>
                    </div>
                    <div class="text-xs text-gray-500 mt-0.5">
                      <%= entry.referrals_count * rand(20..50) %> reward points
                    </div>
                  <% else %>
                    <span class="text-sm text-gray-500">—</span>
                  <% end %>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">
                    <%= entry.created_at.strftime('%b %d, %Y') %>
                  </div>
                  <div class="text-xs text-gray-500">
                    <%= time_ago_in_words(entry.created_at) %> ago
                  </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <% source = ['Direct', 'Twitter', 'Facebook', 'Email', 'Referral'][rand(5)] %>
                    <% source_colors = {
                      'Direct' => 'bg-gray-100 text-gray-800',
                      'Twitter' => 'bg-blue-100 text-blue-800',
                      'Facebook' => 'bg-indigo-100 text-indigo-800',
                      'Email' => 'bg-purple-100 text-purple-800',
                      'Referral' => 'bg-emerald-100 text-emerald-800'
                    } %>
                    
                    <span class="px-2 py-1 text-xs font-medium rounded-md <%= source_colors[source] %>">
                      <%= source %>
                    </span>
                  </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-3">
                    <!-- View button -->
                    <%= link_to project_landing_page_waitlist_entry_path(@project, @landing_page, entry),
                              class: "text-indigo-600 hover:text-indigo-900 transition-colors" do %>
                      <span class="sr-only">View details</span>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                      </svg>
                    <% end %>
                    
                    <!-- Email button -->
                    <button class="text-blue-600 hover:text-blue-800 transition-colors">
                      <span class="sr-only">Send email</span>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </button>
                    
                    <!-- Delete button -->
                    <%= link_to project_landing_page_waitlist_entry_path(@project, @landing_page, entry),
                              method: :delete,
                              data: { 
                                turbo_method: :delete,
                                turbo_confirm: 'Are you sure you want to remove this subscriber?' 
                              },
                              class: "text-red-600 hover:text-red-900 transition-colors" do %>
                      <span class="sr-only">Delete</span>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    <% end %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
      
      <!-- Pagination Controls -->
      <div class="bg-white px-6 py-4 border-t border-gray-200 flex items-center justify-between">
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing <span class="font-medium">1</span> to <span class="font-medium"><%= [@waitlist_entries.count, 10].min %></span> of <span class="font-medium"><%= @waitlist_entries.count %></span> results
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <span class="sr-only">Previous</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </a>
              <a href="#" aria-current="page" class="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600">
                1
              </a>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                2
              </a>
              <a href="#" class="hidden md:inline-flex relative items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                3
              </a>
              <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                ...
              </span>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <%= (@waitlist_entries.count / 10.0).ceil %>
              </a>
              <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <span class="sr-only">Next</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </a>
            </nav>
          </div>
        </div>
      </div>
    <% else %>
      <!-- Empty state with illustration -->
      <div class="text-center py-16 px-6">
        <div class="mx-auto w-36 h-36 bg-indigo-100 rounded-full flex items-center justify-center mb-6">
          <svg class="w-20 h-20 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
          </svg>
        </div>
        <h3 class="mt-2 text-xl font-bold text-gray-900">No waitlist entries yet</h3>
        <p class="mt-3 max-w-md mx-auto text-base text-gray-500">
          Get started by sharing your landing page with your audience or driving traffic through social media.
        </p>
        <div class="mt-8 flex flex-col sm:flex-row gap-3 justify-center">
          <%= link_to project_landing_page_path(@project, @landing_page), 
                    class: "inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            View Landing Page
          <% end %>
          
          <button class="inline-flex items-center px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-indigo-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
            </svg>
            Share Your Waitlist
          </button>
        </div>
      </div>
    <% end %>
  </div>
</div>

<!-- Analytics Teaser Card -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-8 mb-16">
  <div class="bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl shadow-xl overflow-hidden">
    <div class="px-6 py-8 md:px-10 md:py-10 md:flex md:items-center md:justify-between">
      <div class="md:flex-1">
        <h2 class="text-2xl font-bold text-white">Want deeper insights?</h2>
        <p class="mt-2 text-violet-100 max-w-3xl">
          Upgrade to Pro for advanced analytics, audience segmentation, email automation, and more powerful tools to grow your waitlist.
        </p>
      </div>
      <div class="mt-6 md:mt-0 md:ml-10 flex">
        <button class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-violet-700 bg-white hover:bg-violet-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white focus:ring-offset-violet-600">
          Upgrade Now
        </button>
      </div>
    </div>
  </div>
</div>
