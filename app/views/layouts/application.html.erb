<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Waitlistbuilder" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gradient-to-br from-slate-50 via-white to-slate-50 min-h-screen flex flex-col">
    <!-- Navigation -->
    <%= render 'shared/navbar' %>

    <!-- Flash messages -->
    <% if notice %>
      <div class="fixed top-24 right-6 z-50 max-w-sm">
        <div class="bg-emerald-50 border border-emerald-200 text-emerald-800 px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm" role="alert">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <%= notice %>
          </div>
        </div>
      </div>
    <% end %>
    
    <% if alert %>
      <div class="fixed top-24 right-6 z-50 max-w-sm">
        <div class="bg-red-50 border border-red-200 text-red-800 px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm" role="alert">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <%= alert %>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Main content -->
    <main class="">
      <div class="px-4 sm:px-0">
        <%= yield %>
      </div>
    </main>

    <!-- Footer -->
    <%= render 'shared/footer' %>

    <!-- Background decorative elements -->
    <div class="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-32 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl"></div>
      <div class="absolute -bottom-40 -left-32 w-80 h-80 bg-gradient-to-r from-emerald-400/20 to-teal-400/20 rounded-full blur-3xl"></div>
    </div>
  </body>
</html>
