<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    
    <!-- SEO Meta Tags -->
    <title><%= @page_title || @landing_page&.seo_title || "Beautiful Waitlist Landing Page" %></title>
    <meta name="description" content="<%= @page_description || @landing_page&.seo_description || "Join our exclusive waitlist and be the first to know when we launch!" %>">
    <meta name="keywords" content="<%= @page_keywords || @landing_page&.seo_keywords || "waitlist, launch, early access, beta" %>">
    <meta name="robots" content="<%= @robots_meta || "index, follow" %>">
    
    <!-- Canonical URL -->
    <% if @canonical_url.present? %>
      <link rel="canonical" href="<%= @canonical_url %>">
    <% end %>
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="<%= @og_type || "website" %>">
    <meta property="og:url" content="<%= @og_url || request.original_url %>">
    <meta property="og:title" content="<%= @og_title || @page_title %>">
    <meta property="og:description" content="<%= @og_description || @page_description %>">
    <% if @og_image.present? %>
      <meta property="og:image" content="<%= @og_image %>">
    <% end %>
    
    <!-- Twitter -->
    <meta property="twitter:card" content="<%= @twitter_card || "summary_large_image" %>">
    <meta property="twitter:url" content="<%= request.original_url %>">
    <meta property="twitter:title" content="<%= @twitter_title || @page_title %>">
    <meta property="twitter:description" content="<%= @twitter_description || @page_description %>">
    <% if @twitter_image.present? %>
      <meta property="twitter:image" content="<%= @twitter_image %>">
    <% end %>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/icon.svg">
    
    <!-- Google Fonts - Inter for modern, clean typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    
    <!-- Custom CSS for this landing page -->
    <% if @landing_page&.custom_css.present? %>
      <style><%= @landing_page.custom_css.html_safe %></style>
    <% end %>
    
    <!-- Dynamic color scheme -->
    <% if @landing_page.present? %>
      <style>
        :root {
          --primary-color: <%= @landing_page.primary_color || '#3B82F6' %>;
          --secondary-color: <%= @landing_page.secondary_color || '#6366F1' %>;
          --primary-rgb: <%= @landing_page.primary_color_rgb.join(', ') rescue '59, 130, 246' %>;
          --secondary-rgb: <%= @landing_page.secondary_color_rgb.join(', ') rescue '99, 102, 241' %>;
        }
        
        .bg-primary { background-color: var(--primary-color); }
        .bg-secondary { background-color: var(--secondary-color); }
        .text-primary { color: var(--primary-color); }
        .text-secondary { color: var(--secondary-color); }
        .border-primary { border-color: var(--primary-color); }
        .border-secondary { border-color: var(--secondary-color); }
        
        .bg-primary-gradient {
          background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }
        
        .glass-effect {
          background: rgba(255, 255, 255, 0.25);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .text-gradient {
          background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      </style>
    <% end %>
    
    <!-- Schema.org structured data -->
    <% if @schema_markup.present? %>
      <script type="application/ld+json">
        <%= @schema_markup.html_safe %>
      </script>
    <% end %>
  </head>

  <body class="bg-white text-slate-900 antialiased" style="font-family: Inter, system-ui, -apple-system, sans-serif;">
    <!-- Background Pattern -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-indigo-50"></div>
      <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.05) 2px, transparent 0), radial-gradient(circle at 75px 75px, rgba(99, 102, 241, 0.05) 2px, transparent 0); background-size: 100px 100px;"></div>
    </div>
    
    <%= yield %>
    
    <!-- JavaScript -->
    <%= javascript_importmap_tags %>
    
    <!-- Analytics and tracking scripts would go here in production -->
  </body>
</html>