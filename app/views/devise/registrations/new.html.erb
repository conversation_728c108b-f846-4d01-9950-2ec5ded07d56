<% content_for :title, "Get Started - WaitlistBuilder" %>

<div class="min-h-[calc(100vh-5rem)] flex items-center justify-center py-12">
  <div class="max-w-md w-full mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl shadow-lg flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-slate-900 mb-2">Get started free</h1>
      <p class="text-slate-600">Create your WaitlistBuilder account</p>
    </div>

    <!-- Form Card -->
    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 p-8">
      <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { class: "space-y-6" }) do |f| %>
        
        <!-- Error Messages -->
        <% if resource.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-xl p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Please fix the following error<%= 's' if resource.errors.count > 1 %>:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% resource.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Name Fields -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <%= f.label :first_name, class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= f.text_field :first_name, 
                           autofocus: true,
                           class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                           placeholder: "First name" %>
          </div>
          <div>
            <%= f.label :last_name, class: "block text-sm font-medium text-slate-700 mb-2" %>
            <%= f.text_field :last_name, 
                           class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                           placeholder: "Last name" %>
          </div>
        </div>

        <!-- Email Field -->
        <div>
          <%= f.label :email, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= f.email_field :email, 
                           autocomplete: "email",
                           class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                           placeholder: "Enter your email address" %>
        </div>

        <!-- Password Field -->
        <div>
          <div class="flex justify-between items-center mb-2">
            <%= f.label :password, class: "block text-sm font-medium text-slate-700" %>
            <% if @minimum_password_length %>
              <span class="text-xs text-slate-500">
                <%= @minimum_password_length %> characters minimum
              </span>
            <% end %>
          </div>
          <%= f.password_field :password, 
                              autocomplete: "new-password",
                              class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                              placeholder: "Create a password" %>
        </div>

        <!-- Password Confirmation Field -->
        <div>
          <%= f.label :password_confirmation, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= f.password_field :password_confirmation, 
                              autocomplete: "new-password",
                              class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                              placeholder: "Confirm your password" %>
        </div>

        <!-- Terms Agreement -->
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input id="terms" name="terms" type="checkbox" class="w-4 h-4 text-emerald-600 bg-gray-100 border-gray-300 rounded focus:ring-emerald-500 focus:ring-2" required>
          </div>
          <div class="ml-3 text-sm">
            <label for="terms" class="text-slate-600">
              I agree to the 
              <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium">Terms of Service</a>
              and 
              <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium">Privacy Policy</a>
            </label>
          </div>
        </div>

        <!-- Submit Button -->
        <div>
          <%= f.submit "Create account", 
                      class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-white bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 font-medium text-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg" %>
        </div>
      <% end %>
    </div>

    <!-- Footer Links -->
    <div class="text-center mt-6">
      <p class="text-slate-600">
        Already have an account?
        <%= link_to "Sign in", 
                    new_session_path(resource_name), 
                    class: "font-medium text-emerald-600 hover:text-emerald-700 ml-1" %>
      </p>
    </div>
  </div>
</div>