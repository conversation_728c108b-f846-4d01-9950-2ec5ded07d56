<% content_for :title, "Reset Password - WaitlistBuilder" %>

<div class="min-h-[calc(100vh-5rem)] flex items-center justify-center py-12">
  <div class="max-w-md w-full mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl shadow-lg flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8v2.25H5.25v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121 9z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-slate-900 mb-2">Forgot password?</h1>
      <p class="text-slate-600">No worries! We'll send you reset instructions</p>
    </div>

    <!-- Form Card -->
    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 p-8">
      <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post, class: "space-y-6" }) do |f| %>
        
        <!-- Error Messages -->
        <% if resource.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-xl p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Please fix the following error<%= 's' if resource.errors.count > 1 %>:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% resource.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Email Field -->
        <div>
          <%= f.label :email, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= f.email_field :email, 
                           autofocus: true, 
                           autocomplete: "email",
                           class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                           placeholder: "Enter your email address" %>
          <p class="mt-2 text-sm text-slate-500">
            We'll send password reset instructions to this email address
          </p>
        </div>

        <!-- Submit Button -->
        <div>
          <%= f.submit "Send reset instructions", 
                      class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 font-medium text-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg" %>
        </div>
      <% end %>
    </div>

    <!-- Footer Links -->
    <div class="text-center mt-6 space-y-2">
      <p class="text-slate-600">
        Remember your password?
        <%= link_to "Sign in", 
                    new_session_path(resource_name), 
                    class: "font-medium text-purple-600 hover:text-purple-700 ml-1" %>
      </p>
      <p class="text-slate-600">
        Don't have an account?
        <%= link_to "Sign up", 
                    new_registration_path(resource_name), 
                    class: "font-medium text-purple-600 hover:text-purple-700 ml-1" %>
      </p>
    </div>
  </div>
</div>