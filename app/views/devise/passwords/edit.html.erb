<% content_for :title, "Set New Password - WaitlistBuilder" %>

<div class="min-h-[calc(100vh-5rem)] flex items-center justify-center py-12">
  <div class="max-w-md w-full mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl shadow-lg flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-slate-900 mb-2">Set new password</h1>
      <p class="text-slate-600">Choose a strong password for your account</p>
    </div>

    <!-- Form Card -->
    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 p-8">
      <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put, class: "space-y-6" }) do |f| %>
        <%= f.hidden_field :reset_password_token %>
        
        <!-- Error Messages -->
        <% if resource.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-xl p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Please fix the following error<%= 's' if resource.errors.count > 1 %>:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% resource.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- New Password Field -->
        <div>
          <div class="flex justify-between items-center mb-2">
            <%= f.label :password, "New password", class: "block text-sm font-medium text-slate-700" %>
            <% if @minimum_password_length %>
              <span class="text-xs text-slate-500">
                <%= @minimum_password_length %> characters minimum
              </span>
            <% end %>
          </div>
          <%= f.password_field :password, 
                              autofocus: true, 
                              autocomplete: "new-password",
                              class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                              placeholder: "Enter your new password" %>
        </div>

        <!-- Password Confirmation Field -->
        <div>
          <%= f.label :password_confirmation, "Confirm new password", class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= f.password_field :password_confirmation, 
                              autocomplete: "new-password",
                              class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                              placeholder: "Confirm your new password" %>
        </div>

        <!-- Password Strength Tips -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">Tips for a strong password:</h3>
              <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc pl-5 space-y-1">
                  <li>Use at least 8 characters</li>
                  <li>Include uppercase and lowercase letters</li>
                  <li>Add numbers and special characters</li>
                  <li>Avoid common words or personal information</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div>
          <%= f.submit "Update password", 
                      class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-white bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 font-medium text-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg" %>
        </div>
      <% end %>
    </div>

    <!-- Footer Links -->
    <div class="text-center mt-6">
      <p class="text-slate-600">
        Remember your password?
        <%= link_to "Sign in", 
                    new_session_path(resource_name), 
                    class: "font-medium text-indigo-600 hover:text-indigo-700 ml-1" %>
      </p>
    </div>
  </div>
</div>