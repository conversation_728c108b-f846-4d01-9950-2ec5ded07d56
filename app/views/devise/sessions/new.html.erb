<% content_for :title, "Sign In - WaitlistBuilder" %>

<div class="min-h-[calc(100vh-5rem)] flex items-center justify-center py-12">
  <div class="max-w-md w-full mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-slate-900 mb-2">Welcome back</h1>
      <p class="text-slate-600">Sign in to your WaitlistBuilder account</p>
    </div>

    <!-- Form Card -->
    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 p-8">
      <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: { class: "space-y-6" }) do |f| %>
        
        <!-- Email Field -->
        <div>
          <%= f.label :email, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= f.email_field :email, 
                           autofocus: true, 
                           autocomplete: "email",
                           class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                           placeholder: "Enter your email address" %>
        </div>

        <!-- Password Field -->
        <div>
          <%= f.label :password, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= f.password_field :password, 
                              autocomplete: "current-password",
                              class: "w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/70 backdrop-blur-sm",
                              placeholder: "Enter your password" %>
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between">
          <% if devise_mapping.rememberable? %>
            <div class="flex items-center">
              <%= f.check_box :remember_me, class: "w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" %>
              <%= f.label :remember_me, "Remember me", class: "ml-2 text-sm text-slate-600" %>
            </div>
          <% else %>
            <div></div>
          <% end %>
          
          <%= link_to "Forgot password?", 
                      new_password_path(resource_name), 
                      class: "text-sm text-blue-600 hover:text-blue-700 font-medium" %>
        </div>

        <!-- Submit Button -->
        <div>
          <%= f.submit "Sign in", 
                      class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 font-medium text-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg" %>
        </div>
      <% end %>
    </div>

    <!-- Footer Links -->
    <div class="text-center mt-6">
      <p class="text-slate-600">
        Don't have an account?
        <%= link_to "Sign up", 
                    new_registration_path(resource_name), 
                    class: "font-medium text-blue-600 hover:text-blue-700 ml-1" %>
      </p>
    </div>
  </div>
</div>