/* Enhanced Form Styling */

/* Custom focus ring for better accessibility */
.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Smooth transitions for all form elements */
.form-input,
.form-textarea,
.form-select,
.form-checkbox {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover states */
.form-input:hover:not(:focus):not(:disabled) {
  border-color: rgb(148, 163, 184);
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Custom checkbox styling */
.form-checkbox {
  appearance: none;
  background-color: white;
  border: 2px solid rgb(203, 213, 225);
  border-radius: 0.375rem;
  width: 1.25rem;
  height: 1.25rem;
  position: relative;
  cursor: pointer;
}

.form-checkbox:checked {
  background-color: rgb(59, 130, 246);
  border-color: rgb(59, 130, 246);
}

.form-checkbox:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.375rem;
  height: 0.625rem;
  border: 2px solid white;
  border-top: none;
  border-left: none;
  transform: translate(-50%, -60%) rotate(45deg);
}

/* Form field error states */
.field-error {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced button hover effects */
.btn-primary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* Form section dividers */
.form-section {
  position: relative;
}

.form-section::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgb(203, 213, 225),
    transparent
  );
}

/* Progress indicator styling */
.progress-step {
  position: relative;
}

.progress-step::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -2rem;
  width: 1rem;
  height: 2px;
  background-color: rgb(203, 213, 225);
  transform: translateY(-50%);
}

.progress-step.active::after {
  background-color: rgb(59, 130, 246);
}

/* Floating label effect */
.floating-label {
  position: relative;
}

.floating-label input:focus + label,
.floating-label input:not(:placeholder-shown) + label {
  transform: translateY(-1.5rem) scale(0.875);
  color: rgb(59, 130, 246);
}

.floating-label label {
  position: absolute;
  top: 0.75rem;
  left: 1rem;
  transition: all 0.2s ease-out;
  pointer-events: none;
  background: white;
  padding: 0 0.25rem;
}

/* Custom scrollbar for textareas */
.form-textarea::-webkit-scrollbar {
  width: 0.5rem;
}

.form-textarea::-webkit-scrollbar-track {
  background: rgb(248, 250, 252);
  border-radius: 0.25rem;
}

.form-textarea::-webkit-scrollbar-thumb {
  background: rgb(203, 213, 225);
  border-radius: 0.25rem;
}

.form-textarea::-webkit-scrollbar-thumb:hover {
  background: rgb(148, 163, 184);
}

/* Responsive form adjustments */
@media (max-width: 640px) {
  .form-container {
    padding: 1rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .form-actions button {
    width: 100%;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form-input,
  .form-textarea,
  .form-select {
    border-width: 2px;
  }
  
  .form-input:focus,
  .form-textarea:focus,
  .form-select:focus {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .form-input,
  .form-textarea,
  .form-select,
  .form-checkbox,
  .btn-primary {
    transition: none;
  }
  
  .animate-spin {
    animation: none;
  }
  
  .field-error {
    animation: none;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .form-input,
  .form-textarea,
  .form-select {
    background-color: rgb(30, 41, 59);
    border-color: rgb(71, 85, 105);
    color: rgb(248, 250, 252);
  }
  
  .form-input::placeholder,
  .form-textarea::placeholder {
    color: rgb(148, 163, 184);
  }
}
