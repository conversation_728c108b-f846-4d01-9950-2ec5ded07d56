import { Controller } from "@hotwired/stimulus"

// Smart suggestions controller for project names
export default class extends Controller {
  static targets = ["container", "suggestions"]

  connect() {
    this.suggestions = [
      // Tech/SaaS suggestions
      { keywords: ["task", "todo", "manage"], suggestions: ["TaskFlow Pro", "TaskMaster", "FlowTracker"] },
      { keywords: ["eco", "green", "environment"], suggestions: ["EcoTracker", "GreenPath", "EcoFlow"] },
      { keywords: ["mind", "mental", "wellness"], suggestions: ["MindfulMoments", "WellnessPath", "MindFlow"] },
      { keywords: ["learn", "education", "study"], suggestions: ["LearnHub", "StudyFlow", "EduTracker"] },
      { keywords: ["fit", "health", "exercise"], suggestions: ["FitTracker", "HealthFlow", "WellnessHub"] },
      { keywords: ["money", "finance", "budget"], suggestions: ["MoneyFlow", "BudgetPro", "FinanceTracker"] },
      { keywords: ["social", "connect", "network"], suggestions: ["ConnectHub", "SocialFlow", "NetworkPro"] },
      { keywords: ["photo", "image", "visual"], suggestions: ["PhotoFlow", "VisualHub", "ImagePro"] },
      { keywords: ["music", "audio", "sound"], suggestions: ["SoundFlow", "AudioHub", "MusicPro"] },
      { keywords: ["travel", "trip", "journey"], suggestions: ["TravelFlow", "JourneyHub", "TripTracker"] }
    ]
  }

  generateSuggestions() {
    const input = this.element.value.toLowerCase().trim()
    
    if (input.length < 3) {
      this.hideSuggestions()
      return
    }

    const matchingSuggestions = this.findMatchingSuggestions(input)
    
    if (matchingSuggestions.length > 0) {
      this.showSuggestions(matchingSuggestions)
    } else {
      this.generateGenericSuggestions(input)
    }
  }

  findMatchingSuggestions(input) {
    const matches = []
    
    this.suggestions.forEach(category => {
      category.keywords.forEach(keyword => {
        if (input.includes(keyword)) {
          matches.push(...category.suggestions)
        }
      })
    })
    
    // Remove duplicates and limit to 3
    return [...new Set(matches)].slice(0, 3)
  }

  generateGenericSuggestions(input) {
    const suffixes = ["Pro", "Hub", "Flow", "Tracker", "Master", "Plus"]
    const prefixes = ["Smart", "Quick", "Easy", "Super", "Ultra", "Next"]
    
    const suggestions = []
    const capitalizedInput = this.capitalize(input)
    
    // Add suffix variations
    suffixes.slice(0, 2).forEach(suffix => {
      suggestions.push(`${capitalizedInput} ${suffix}`)
    })
    
    // Add prefix variation
    suggestions.push(`${prefixes[0]} ${capitalizedInput}`)
    
    this.showSuggestions(suggestions)
  }

  showSuggestions(suggestions) {
    if (!this.hasContainerTarget || !this.hasSuggestionsTarget) return
    
    this.suggestionsTarget.innerHTML = ''
    
    suggestions.forEach(suggestion => {
      const button = document.createElement('button')
      button.type = 'button'
      button.className = 'px-3 py-1 text-xs bg-blue-50 text-blue-700 rounded-full hover:bg-blue-100 transition-colors border border-blue-200'
      button.textContent = suggestion
      button.addEventListener('click', () => this.applySuggestion(suggestion))
      
      this.suggestionsTarget.appendChild(button)
    })
    
    this.containerTarget.classList.remove('hidden')
  }

  hideSuggestions() {
    if (this.hasContainerTarget) {
      this.containerTarget.classList.add('hidden')
    }
  }

  applySuggestion(suggestion) {
    this.element.value = suggestion
    this.element.dispatchEvent(new Event('input', { bubbles: true }))
    this.hideSuggestions()
    
    // Focus next field or trigger validation
    this.element.blur()
  }

  capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1)
  }
}
