import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="mobile-menu"
export default class extends Controller {
  static targets = ["menu", "button", "hamburger", "close"]

  connect() {
    // Initialize menu as closed
    this.close()
    // Bind the close method to preserve 'this' context
    this.boundCloseOnClickOutside = this.closeOnClickOutside.bind(this)
  }

  toggle() {
    if (this.menuTarget.classList.contains("hidden")) {
      this.open()
    } else {
      this.close()
    }
  }

  open() {
    this.menuTarget.classList.remove("hidden")
    this.menuTarget.classList.add("animate-fadeIn")
    
    // Show close icon, hide hamburger
    if (this.hasHamburgerTarget) this.hamburgerTarget.classList.add("hidden")
    if (this.hasCloseTarget) this.closeTarget.classList.remove("hidden")
    
    // Add aria attributes
    this.buttonTarget.setAttribute("aria-expanded", "true")
    this.menuTarget.setAttribute("aria-hidden", "false")

    // Add click outside to close
    setTimeout(() => {
      document.addEventListener("click", this.boundCloseOnClickOutside)
    }, 100)
  }

  close() {
    this.menuTarget.classList.add("hidden")
    this.menuTarget.classList.remove("animate-fadeIn")
    
    // Show hamburger icon, hide close
    if (this.hasHamburgerTarget) this.hamburgerTarget.classList.remove("hidden")
    if (this.hasCloseTarget) this.closeTarget.classList.add("hidden")
    
    // Add aria attributes
    this.buttonTarget.setAttribute("aria-expanded", "false")
    this.menuTarget.setAttribute("aria-hidden", "true")

    // Remove click outside listener
    document.removeEventListener("click", this.boundCloseOnClickOutside)
  }

  closeOnClickOutside(event) {
    if (!this.element.contains(event.target)) {
      this.close()
    }
  }

  // Close menu when clicking on a link (for better UX)
  closeMenu() {
    this.close()
  }

  disconnect() {
    // Clean up event listeners
    document.removeEventListener("click", this.boundCloseOnClickOutside)
  }
}