// Import and register all your controllers from the importmap via controllers/**/*_controller
import { application } from "controllers/application"
import { eagerLoadControllersFrom } from "@hotwired/stimulus-loading"
eagerLoadControllersFrom("controllers", application)

// Import form enhancement controllers
import FormValidationController from "./form_validation_controller"
import CharacterCounterController from "./character_counter_controller"
import DisclosureController from "./disclosure_controller"
import LoadingButtonController from "./loading_button_controller"
import AutoResizeController from "./auto_resize_controller"
import LogoPreviewController from "./logo_preview_controller"
import FormSaverController from "./form_saver_controller"
import SmartSuggestionsController from "./smart_suggestions_controller"
import TooltipController from "./tooltip_controller"

// Register form enhancement controllers
application.register("form-validation", FormValidationController)
application.register("character-counter", CharacterCounterController)
application.register("disclosure", DisclosureController)
application.register("loading-button", LoadingButtonController)
application.register("auto-resize", AutoResizeController)
application.register("logo-preview", LogoPreviewController)
application.register("form-saver", FormSaverController)
application.register("smart-suggestions", SmartSuggestionsController)
application.register("tooltip", TooltipController)
