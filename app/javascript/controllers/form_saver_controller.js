import { Controller } from "@hotwired/stimulus"

// Form auto-save and draft functionality
export default class extends Controller {
  connect() {
    this.setupAutoSave()
    this.loadDraft()
  }

  setupAutoSave() {
    // Auto-save every 30 seconds
    this.autoSaveInterval = setInterval(() => {
      this.saveDraft(true) // silent save
    }, 30000)
    
    // Save on form field changes (debounced)
    const fields = this.element.querySelectorAll('input, textarea, select')
    fields.forEach(field => {
      field.addEventListener('input', this.debounce(() => {
        this.saveDraft(true)
      }, 2000))
    })
  }

  saveDraft(silent = false) {
    const formData = new FormData(this.element)
    const draftData = {}
    
    // Convert FormData to object
    for (let [key, value] of formData.entries()) {
      if (key.includes('[') && key.includes(']')) {
        // Handle nested attributes like project[name]
        const match = key.match(/(\w+)\[(\w+)\]/)
        if (match) {
          const [, model, attribute] = match
          if (!draftData[model]) draftData[model] = {}
          draftData[model][attribute] = value
        }
      } else {
        draftData[key] = value
      }
    }
    
    // Save to localStorage
    localStorage.setItem('project_form_draft', JSON.stringify(draftData))
    
    if (!silent) {
      this.showSaveConfirmation()
    }
  }

  loadDraft() {
    const draftData = localStorage.getItem('project_form_draft')
    if (draftData) {
      try {
        const data = JSON.parse(draftData)
        this.populateForm(data)
      } catch (e) {
        console.error('Error loading draft:', e)
      }
    }
  }

  populateForm(data) {
    Object.keys(data).forEach(model => {
      if (typeof data[model] === 'object') {
        Object.keys(data[model]).forEach(attribute => {
          const field = this.element.querySelector(`[name="${model}[${attribute}]"]`)
          if (field && !field.value) { // Only populate if field is empty
            field.value = data[model][attribute]
            
            // Trigger events for Stimulus controllers
            field.dispatchEvent(new Event('input', { bubbles: true }))
          }
        })
      }
    })
  }

  showSaveConfirmation() {
    // Create temporary notification
    const notification = document.createElement('div')
    notification.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity'
    notification.textContent = '💾 Draft saved'
    
    document.body.appendChild(notification)
    
    // Remove after 2 seconds
    setTimeout(() => {
      notification.style.opacity = '0'
      setTimeout(() => {
        document.body.removeChild(notification)
      }, 300)
    }, 2000)
  }

  clearDraft() {
    localStorage.removeItem('project_form_draft')
  }

  disconnect() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
    }
  }

  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }
}
