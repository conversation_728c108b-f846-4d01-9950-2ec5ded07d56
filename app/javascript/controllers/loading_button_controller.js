import { Controller } from "@hotwired/stimulus"

// Loading button controller for form submission feedback
export default class extends Controller {
  static targets = ["text", "spinner", "icon"]
  static values = { text: String }

  showLoading() {
    // Disable the button
    this.element.disabled = true
    
    // Update text
    if (this.hasTextTarget) {
      this.originalText = this.textTarget.textContent
      this.textTarget.textContent = this.textValue || 'Loading...'
    }
    
    // Show spinner, hide icon
    if (this.hasSpinnerTarget && this.hasIconTarget) {
      this.iconTarget.classList.add('hidden')
      this.spinnerTarget.classList.remove('hidden')
      this.spinnerTarget.classList.add('animate-spin')
    }
    
    // Add loading styling
    this.element.classList.add('opacity-75', 'cursor-not-allowed')
  }

  hideLoading() {
    // Re-enable the button
    this.element.disabled = false
    
    // Restore text
    if (this.hasTextTarget && this.originalText) {
      this.textTarget.textContent = this.originalText
    }
    
    // Hide spinner, show icon
    if (this.hasSpinnerTarget && this.hasIconTarget) {
      this.spinnerTarget.classList.add('hidden')
      this.spinnerTarget.classList.remove('animate-spin')
      this.iconTarget.classList.remove('hidden')
    }
    
    // Remove loading styling
    this.element.classList.remove('opacity-75', 'cursor-not-allowed')
  }
}
