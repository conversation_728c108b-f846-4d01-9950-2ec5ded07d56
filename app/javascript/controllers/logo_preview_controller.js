import { Controller } from "@hotwired/stimulus"

// Logo preview controller for URL inputs
export default class extends Controller {
  static targets = ["preview", "image"]

  updatePreview() {
    const url = this.element.value.trim()
    
    if (url && this.isValidImageUrl(url)) {
      this.showPreview(url)
    } else {
      this.hidePreview()
    }
  }

  showPreview(url) {
    if (this.hasImageTarget) {
      this.imageTarget.src = url
      this.imageTarget.onload = () => {
        this.previewTarget.classList.remove('hidden')
      }
      this.imageTarget.onerror = () => {
        this.hidePreview()
      }
    }
  }

  hidePreview() {
    if (this.hasPreviewTarget) {
      this.previewTarget.classList.add('hidden')
    }
  }

  isValidImageUrl(url) {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname.toLowerCase()
      return pathname.match(/\.(jpg|jpeg|png|gif|svg|webp)$/)
    } catch (_) {
      return false
    }
  }
}
