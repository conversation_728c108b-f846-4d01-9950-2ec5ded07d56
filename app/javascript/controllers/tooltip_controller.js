import { Controller } from "@hotwired/stimulus"

// Tooltip controller for contextual help
export default class extends Controller {
  static values = { content: String, position: String }

  connect() {
    this.positionValue = this.positionValue || 'top'
    this.setupTooltip()
  }

  setupTooltip() {
    this.element.addEventListener('mouseenter', () => this.show())
    this.element.addEventListener('mouseleave', () => this.hide())
    this.element.addEventListener('focus', () => this.show())
    this.element.addEventListener('blur', () => this.hide())
  }

  show() {
    if (this.tooltip) return
    
    this.tooltip = document.createElement('div')
    this.tooltip.className = `
      absolute z-50 px-3 py-2 text-sm text-white bg-slate-900 rounded-lg shadow-lg
      max-w-xs pointer-events-none transition-opacity duration-200
    `.trim()
    
    this.tooltip.textContent = this.contentValue
    
    // Position the tooltip
    this.positionTooltip()
    
    document.body.appendChild(this.tooltip)
    
    // Fade in
    requestAnimationFrame(() => {
      this.tooltip.style.opacity = '1'
    })
  }

  hide() {
    if (!this.tooltip) return
    
    this.tooltip.style.opacity = '0'
    
    setTimeout(() => {
      if (this.tooltip && this.tooltip.parentNode) {
        this.tooltip.parentNode.removeChild(this.tooltip)
      }
      this.tooltip = null
    }, 200)
  }

  positionTooltip() {
    const rect = this.element.getBoundingClientRect()
    const tooltipRect = this.tooltip.getBoundingClientRect()
    
    let top, left
    
    switch (this.positionValue) {
      case 'bottom':
        top = rect.bottom + window.scrollY + 8
        left = rect.left + window.scrollX + (rect.width / 2) - (tooltipRect.width / 2)
        break
      case 'left':
        top = rect.top + window.scrollY + (rect.height / 2) - (tooltipRect.height / 2)
        left = rect.left + window.scrollX - tooltipRect.width - 8
        break
      case 'right':
        top = rect.top + window.scrollY + (rect.height / 2) - (tooltipRect.height / 2)
        left = rect.right + window.scrollX + 8
        break
      default: // top
        top = rect.top + window.scrollY - tooltipRect.height - 8
        left = rect.left + window.scrollX + (rect.width / 2) - (tooltipRect.width / 2)
    }
    
    // Ensure tooltip stays within viewport
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    if (left < 8) left = 8
    if (left + tooltipRect.width > viewportWidth - 8) {
      left = viewportWidth - tooltipRect.width - 8
    }
    
    if (top < 8) top = 8
    if (top + tooltipRect.height > viewportHeight - 8) {
      top = viewportHeight - tooltipRect.height - 8
    }
    
    this.tooltip.style.position = 'absolute'
    this.tooltip.style.top = `${top}px`
    this.tooltip.style.left = `${left}px`
    this.tooltip.style.opacity = '0'
  }

  disconnect() {
    this.hide()
  }
}
