import { Controller } from "@hotwired/stimulus"

// Auto-resize textarea controller
export default class extends Controller {
  connect() {
    this.resize()
  }

  resize() {
    // Reset height to auto to get the correct scrollHeight
    this.element.style.height = 'auto'
    
    // Set height to scrollHeight to fit content
    this.element.style.height = this.element.scrollHeight + 'px'
    
    // Ensure minimum height
    const minHeight = 100 // minimum height in pixels
    if (this.element.scrollHeight < minHeight) {
      this.element.style.height = minHeight + 'px'
    }
  }
}
