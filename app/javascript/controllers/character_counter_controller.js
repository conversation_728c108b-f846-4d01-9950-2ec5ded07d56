import { Controller } from "@hotwired/stimulus"

// Character counter controller for form fields
export default class extends Controller {
  static targets = ["counter"]
  static values = { max: Number }

  connect() {
    this.update()
  }

  update() {
    const currentLength = this.element.value.length
    const maxLength = this.maxValue
    
    if (this.hasCounterTarget) {
      this.counterTarget.textContent = `${currentLength}/${maxLength}`
      
      // Update styling based on character count
      if (currentLength > maxLength * 0.9) {
        this.counterTarget.classList.remove('text-slate-500')
        this.counterTarget.classList.add('text-orange-500')
      } else if (currentLength > maxLength) {
        this.counterTarget.classList.remove('text-slate-500', 'text-orange-500')
        this.counterTarget.classList.add('text-red-500')
      } else {
        this.counterTarget.classList.remove('text-orange-500', 'text-red-500')
        this.counterTarget.classList.add('text-slate-500')
      }
    }
    
    // Update field styling if over limit
    if (currentLength > maxLength) {
      this.element.classList.add('border-red-300', 'focus:border-red-500', 'focus:ring-red-500')
      this.element.classList.remove('border-slate-200', 'focus:border-blue-500', 'focus:ring-blue-500')
    } else {
      this.element.classList.remove('border-red-300', 'focus:border-red-500', 'focus:ring-red-500')
      this.element.classList.add('border-slate-200', 'focus:border-blue-500', 'focus:ring-blue-500')
    }
  }
}
