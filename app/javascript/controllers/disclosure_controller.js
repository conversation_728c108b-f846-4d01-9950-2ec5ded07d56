import { Controller } from "@hotwired/stimulus"

// Progressive disclosure controller for optional form sections
export default class extends Controller {
  static targets = ["content", "toggleText", "icon"]

  connect() {
    this.isOpen = false
  }

  toggle() {
    this.isOpen = !this.isOpen

    if (this.isOpen) {
      this.show()
    } else {
      this.hide()
    }

    // Update ARIA attributes
    this.element.setAttribute('aria-expanded', this.isOpen.toString())
  }

  show() {
    this.contentTarget.classList.remove('hidden')
    this.toggleTextTarget.textContent = 'Hide Options'
    this.iconTarget.style.transform = 'rotate(180deg)'
    
    // Smooth scroll to reveal content
    setTimeout(() => {
      this.contentTarget.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'nearest' 
      })
    }, 100)
  }

  hide() {
    this.contentTarget.classList.add('hidden')
    this.toggleTextTarget.textContent = 'Show Options'
    this.iconTarget.style.transform = 'rotate(0deg)'
  }
}
