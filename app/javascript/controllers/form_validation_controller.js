import { Controller } from "@hotwired/stimulus"

// Real-time form validation controller
export default class extends Controller {
  static targets = ["summary"]
  static values = { 
    requiredFields: Array,
    validationRules: Object 
  }

  connect() {
    this.validateForm()
    this.setupFieldValidation()
  }

  setupFieldValidation() {
    // Add real-time validation to all form fields
    const fields = this.element.querySelectorAll('input, textarea, select')
    
    fields.forEach(field => {
      field.addEventListener('blur', () => this.validateField(field))
      field.addEventListener('input', () => this.clearFieldError(field))
    })
  }

  validateField(field) {
    const fieldName = field.name.split('[').pop().split(']')[0]
    const value = field.value.trim()
    const errors = []

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      errors.push(`${this.getFieldLabel(field)} is required`)
    }

    // Specific field validations
    switch (fieldName) {
      case 'name':
        if (value && value.length > 100) {
          errors.push('Project name must be 100 characters or less')
        }
        if (value && value.length < 2) {
          errors.push('Project name must be at least 2 characters')
        }
        break
        
      case 'description':
        if (value && value.length > 1000) {
          errors.push('Description must be 1000 characters or less')
        }
        break
        
      case 'website_url':
        if (value && !this.isValidUrl(value)) {
          errors.push('Please enter a valid website URL')
        }
        break
        
      case 'logo_url':
        if (value && !this.isValidUrl(value)) {
          errors.push('Please enter a valid logo URL')
        }
        break
    }

    this.displayFieldErrors(field, errors)
    this.validateForm()
  }

  clearFieldError(field) {
    const errorContainer = this.getErrorContainer(field)
    if (errorContainer) {
      errorContainer.remove()
    }
    
    // Remove error styling
    field.classList.remove('border-red-300', 'focus:border-red-500', 'focus:ring-red-500')
    field.classList.add('border-slate-200', 'focus:border-blue-500', 'focus:ring-blue-500')
  }

  displayFieldErrors(field, errors) {
    this.clearFieldError(field)
    
    if (errors.length > 0) {
      // Add error styling
      field.classList.remove('border-slate-200', 'focus:border-blue-500', 'focus:ring-blue-500')
      field.classList.add('border-red-300', 'focus:border-red-500', 'focus:ring-red-500')
      
      // Create error message
      const errorContainer = document.createElement('div')
      errorContainer.className = 'field-error mt-1 text-sm text-red-600'
      errorContainer.innerHTML = `
        <div class="flex items-center space-x-1">
          <svg class="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
          <span>${errors[0]}</span>
        </div>
      `
      
      field.parentNode.appendChild(errorContainer)
    }
  }

  validateForm() {
    const requiredFields = this.element.querySelectorAll('[required]')
    let isValid = true
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        isValid = false
      }
    })
    
    // Update submit button state
    const submitButton = this.element.querySelector('[type="submit"]')
    if (submitButton) {
      submitButton.disabled = !isValid
    }
    
    // Update validation summary
    if (this.hasSummaryTarget) {
      if (isValid) {
        this.summaryTarget.classList.remove('hidden')
        this.summaryTarget.innerHTML = '<span class="text-green-600">✓</span> Ready to create'
      } else {
        this.summaryTarget.classList.add('hidden')
      }
    }
  }

  handleSubmit(event) {
    // Final validation before submit
    const fields = this.element.querySelectorAll('input, textarea, select')
    let hasErrors = false
    
    fields.forEach(field => {
      this.validateField(field)
      if (field.parentNode.querySelector('.field-error')) {
        hasErrors = true
      }
    })
    
    if (hasErrors) {
      event.preventDefault()
      this.showValidationSummary()
    }
  }

  showValidationSummary() {
    const errors = this.element.querySelectorAll('.field-error')
    if (errors.length > 0) {
      // Scroll to first error
      errors[0].scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }

  getFieldLabel(field) {
    const label = this.element.querySelector(`label[for="${field.id}"]`)
    return label ? label.textContent.trim() : field.name
  }

  getErrorContainer(field) {
    return field.parentNode.querySelector('.field-error')
  }

  isValidUrl(string) {
    try {
      const url = new URL(string)
      return url.protocol === 'http:' || url.protocol === 'https:'
    } catch (_) {
      return false
    }
  }
}
