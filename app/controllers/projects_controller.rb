class ProjectsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_project, only: [:show, :edit, :update, :destroy]

  def index
    @projects = current_account.projects.includes(:landing_pages, :waitlist_entries)
    
    # Calculate aggregate statistics
    @stats = {
      total_projects: @projects.count,
      total_landing_pages: current_account.landing_pages.count,
      total_signups: current_account.total_waitlist_entries,
      total_confirmed: current_account.total_confirmed_entries
    }
    @stats[:conversion_rate] = @stats[:total_signups] > 0 ? 
      ((@stats[:total_confirmed].to_f / @stats[:total_signups]) * 100).round(1) : 0
  end

  def show
    @landing_pages = @project.landing_pages.includes(:waitlist_entries)
    
    # Project-specific analytics
    @analytics = {
      total_landing_pages: @landing_pages.count,
      published_pages: @landing_pages.published.count,
      total_signups: ActsAsTenant.without_tenant { @project.waitlist_entries.count },
      confirmed_signups: ActsAsTenant.without_tenant { @project.waitlist_entries.confirmed.count },
      recent_signups: ActsAsTenant.without_tenant { @project.waitlist_entries.recent.order(created_at: :desc).limit(5) }
    }
    @analytics[:conversion_rate] = @analytics[:total_signups] > 0 ? 
      ((@analytics[:confirmed_signups].to_f / @analytics[:total_signups]) * 100).round(1) : 0
  end

  def new
    @project = current_account.projects.build
  end

  def create
    @project = current_account.projects.build(project_params)
    
    if @project.save
      redirect_to @project, notice: 'Project was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @project.update(project_params)
      redirect_to @project, notice: 'Project was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @project.destroy
    redirect_to projects_path, notice: 'Project was successfully deleted.'
  end

  private

  def set_project
    @project = current_account.projects.friendly.find(params[:id])
  end

  def project_params
    params.require(:project).permit(:name, :description, :website_url, :logo_url, :double_opt_in)
  end
end