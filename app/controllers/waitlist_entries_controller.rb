class WaitlistEntriesController < ApplicationController
  # Skip CSRF protection for public signup actions
  skip_before_action :verify_authenticity_token, only: [:create, :confirm, :unsubscribe]
  
  before_action :set_landing_page
  before_action :set_waitlist_entry, only: [:show, :confirm, :unsubscribe]
  
  # Public signup (no authentication required)
  def create
    # Bypass tenant scoping for public signup
    ActsAsTenant.without_tenant do
      @waitlist_entry = @landing_page.waitlist_entries.build(waitlist_entry_params)
      
      # Set default values
      @waitlist_entry.status = 'pending'
      @waitlist_entry.confirmation_token = SecureRandom.urlsafe_base64(32)
      @waitlist_entry.ip_address = request.remote_ip
      @waitlist_entry.user_agent = request.user_agent
      @waitlist_entry.referrer = request.referer
      
      if @waitlist_entry.save
        # Send confirmation email (implement later)
        # WaitlistMailer.confirmation_email(@waitlist_entry).deliver_later
        
        # Track the signup
        @landing_page.increment_page_view! if @landing_page.respond_to?(:increment_page_view!)
        
        # Success message
        flash[:notice] = "Thank you for joining our waitlist! Please check your email to confirm your subscription."
        redirect_to public_landing_page_path(@landing_page)
      else
        # Store errors in flash for display
        flash[:alert] = @waitlist_entry.errors.full_messages.join(", ")
        redirect_to public_landing_page_path(@landing_page)
      end
    end
  end
  
  # Email confirmation
  def confirm
    ActsAsTenant.without_tenant do
      if @waitlist_entry.confirm_email!
        flash[:notice] = "Your email has been confirmed! Welcome to the waitlist."
      else
        flash[:alert] = "Unable to confirm your email. Please try again or contact support."
      end
      
      redirect_to public_landing_page_path(@waitlist_entry.landing_page)
    end
  end
  
  # Unsubscribe
  def unsubscribe
    ActsAsTenant.without_tenant do
      if @waitlist_entry.unsubscribe!
        flash[:notice] = "You have been successfully unsubscribed from the waitlist."
      else
        flash[:alert] = "Unable to unsubscribe. Please contact support."
      end
      
      redirect_to public_landing_page_path(@waitlist_entry.landing_page)
    end
  end
  
  # Admin actions (require authentication)
  before_action :authenticate_user!, only: [:index, :show, :destroy]
  before_action :ensure_admin_access, only: [:index, :show, :destroy]
  
  def index
    @waitlist_entries = @landing_page.waitlist_entries
                                    .includes(:landing_page)
                                    .order(created_at: :desc)
    
    respond_to do |format|
      format.html
      format.json { render json: @waitlist_entries }
      format.csv { 
        headers['Content-Disposition'] = "attachment; filename=\"waitlist-#{@landing_page.title.parameterize}.csv\""
        headers['Content-Type'] = 'text/csv'
        render plain: generate_csv(@waitlist_entries)
      }
    end
  end
  
  def show
    respond_to do |format|
      format.html
      format.json { render json: @waitlist_entry }
    end
  end
  
  def destroy
    @waitlist_entry.destroy
    redirect_to [@landing_page.project, @landing_page, :waitlist_entries], 
                notice: 'Waitlist entry was successfully removed.'
  end
  
  private
  
  def set_landing_page
    if params[:project_id] && params[:landing_page_id]
      # Admin route: /projects/:project_id/landing_pages/:landing_page_id/waitlist_entries
      @project = current_account.projects.friendly.find(params[:project_id])
      @landing_page = @project.landing_pages.friendly.find(params[:landing_page_id])
    else
      # Public route: /page/:landing_page_id/waitlist_entries
      # Bypass tenant scoping for public access
      ActsAsTenant.without_tenant do
        @landing_page = LandingPage.published.friendly.find(params[:landing_page_id])
        @project = @landing_page.project
      end
    end
  rescue ActiveRecord::RecordNotFound
    if params[:project_id]
      redirect_to root_path, alert: 'Landing page not found.'
    else
      render file: 'public/404.html', status: :not_found, layout: false
    end
  end
  
  def set_waitlist_entry
    ActsAsTenant.without_tenant do
      @waitlist_entry = @landing_page.waitlist_entries.find(params[:id])
    end
  rescue ActiveRecord::RecordNotFound
    redirect_to public_landing_page_path(@landing_page), alert: 'Entry not found.'
  end
  
  def waitlist_entry_params
    params.require(:waitlist_entry).permit(:email, :first_name, :last_name, :company, :phone)
  end
  
  def ensure_admin_access
    unless current_user && @project.account == current_account
      redirect_to root_path, alert: 'Access denied.'
    end
  end
  
  def generate_csv(entries)
    require 'csv'
    
    CSV.generate(headers: true) do |csv|
      csv << ['Email', 'First Name', 'Last Name', 'Company', 'Phone', 'Confirmed', 'Joined Date', 'IP Address', 'Referrer']
      
      entries.each do |entry|
        csv << [
          entry.email,
          entry.first_name,
          entry.last_name,
          entry.company,
          entry.phone,
          entry.confirmed? ? 'Yes' : 'No',
          entry.created_at.strftime('%Y-%m-%d %H:%M:%S'),
          entry.ip_address,
          entry.referrer
        ]
      end
    end
  end
end