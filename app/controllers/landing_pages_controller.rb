class LandingPagesController < ApplicationController
  before_action :authenticate_user!, except: [:show, :public_show]
  before_action :set_project, except: [:public_show]
  before_action :set_landing_page, only: [:show, :edit, :update, :destroy, :preview, :duplicate, :toggle_published]
  before_action :set_public_landing_page, only: [:public_show]
  
  # Admin actions (requires authentication)
  def index
    @landing_pages = @project.landing_pages.includes(:waitlist_entries)
                            .order(created_at: :desc)
    
    respond_to do |format|
      format.html
      format.json { render json: @landing_pages }
    end
  end

  def show
    # Admin preview with analytics
    @analytics = {
      page_views: @landing_page.page_views || 0,
      unique_visitors: @landing_page.unique_visitors || 0,
      conversion_rate: @landing_page.conversion_rate || 0,
      total_signups: @landing_page.waitlist_entries.count,
      recent_signups: @landing_page.waitlist_entries.order(created_at: :desc).limit(10)
    }
    
    respond_to do |format|
      format.html
      format.json { render json: @landing_page.as_json(include: :waitlist_entries) }
    end
  end

  def new
    @landing_page = @project.landing_pages.build
    set_seo_defaults
  end

  def create
    @landing_page = @project.landing_pages.build(landing_page_params)
    
    if @landing_page.save
      redirect_to [@project, @landing_page], notice: 'Landing page created successfully!'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    # Pre-fill SEO fields if empty
    set_seo_defaults if @landing_page.meta_title.blank?
  end

  def update
    if @landing_page.update(landing_page_params)
      # If publishing for first time, notify search engines
      if @landing_page.saved_change_to_published? && @landing_page.published?
        flash[:notice] = 'Landing page published successfully! Search engines will be notified.'
      else
        flash[:notice] = 'Landing page updated successfully!'
      end
      
      redirect_to [@project, @landing_page]
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @landing_page.destroy
    redirect_to project_landing_pages_path(@project), notice: 'Landing page deleted successfully!'
  end
  
  # Additional actions
  def preview
    # Preview without incrementing views
    render :public_show, layout: 'landing_page'
  end
  
  def duplicate
    new_landing_page = @landing_page.dup
    new_landing_page.title = "#{@landing_page.title} (Copy)"
    new_landing_page.slug = nil
    new_landing_page.published = false
    new_landing_page.page_views = 0
    new_landing_page.unique_visitors = 0
    
    if new_landing_page.save
      redirect_to [@project, new_landing_page], notice: 'Landing page duplicated successfully!'
    else
      redirect_to [@project, @landing_page], alert: 'Failed to duplicate landing page.'
    end
  end
  
  def toggle_published
    @landing_page.update!(published: !@landing_page.published?)
    
    status = @landing_page.published? ? 'published' : 'unpublished'
    redirect_to [@project, @landing_page], notice: "Landing page #{status} successfully!"
  end

  # Public landing page view (no authentication required)
  def public_show
    # Track page view and get waitlist count (bypass tenant scoping)
    ActsAsTenant.without_tenant do
      @landing_page.increment_page_view!
      @waitlist_count = @landing_page.waitlist_entries.count
    end
    
    # Set SEO meta tags
    set_seo_meta_tags
    
    # Render with landing page layout
    render layout: 'landing_page'
  end
  
  # SEO and utility endpoints
  def sitemap
    @landing_pages = LandingPage.published.includes(:project)
    
    respond_to do |format|
      format.xml { render 'sitemap', layout: false }
    end
  end
  
  def robots
    respond_to do |format|
      format.text { render 'robots', layout: false }
    end
  end

  private

  def set_project
    @project = current_account.projects.friendly.find(params[:project_id])
  rescue ActiveRecord::RecordNotFound
    redirect_to root_path, alert: 'Project not found.'
  end

  def set_landing_page
    @landing_page = @project.landing_pages.friendly.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to project_landing_pages_path(@project), alert: 'Landing page not found.'
  end
  
  def set_public_landing_page
    # Bypass tenant scoping for public access
    ActsAsTenant.without_tenant do
      # For public access via custom domain or slug
      if params[:custom_domain]
        @landing_page = LandingPage.published.find_by!(custom_domain: params[:custom_domain])
      else
        @landing_page = LandingPage.published.friendly.find(params[:id])
      end
      
      @project = @landing_page.project
    end
  rescue ActiveRecord::RecordNotFound
    render file: 'public/404.html', status: :not_found, layout: false
  end

  def landing_page_params
    params.require(:landing_page).permit(
      :title, :subtitle, :description, :hero_image_url,
      :primary_color, :secondary_color, :custom_css,
      :custom_domain, :published,
      # SEO fields
      :meta_title, :meta_description, :meta_keywords,
      :og_title, :og_description, :og_image_url, :og_type,
      :twitter_title, :twitter_description, :twitter_image_url, :twitter_card,
      :canonical_url, :schema_markup, :robots_meta
    )
  end
  
  def set_seo_defaults
    return unless @landing_page.present?
    
    @landing_page.meta_title ||= @landing_page.title
    @landing_page.meta_description ||= @landing_page.description&.truncate(160)
    @landing_page.og_title ||= @landing_page.meta_title
    @landing_page.og_description ||= @landing_page.meta_description
    @landing_page.twitter_title ||= @landing_page.meta_title
    @landing_page.twitter_description ||= @landing_page.meta_description
  end
  
  def set_seo_meta_tags
    # Set instance variables for layout meta tags
    @page_title = @landing_page.seo_title
    @page_description = @landing_page.seo_description
    @page_keywords = @landing_page.seo_keywords
    @page_image = @landing_page.og_image_url || @landing_page.hero_image_url
    @canonical_url = @landing_page.canonical_url_or_default
    
    # Open Graph
    @og_title = @landing_page.og_title_or_default
    @og_description = @landing_page.og_description_or_default
    @og_image = @landing_page.og_image_url
    @og_type = @landing_page.og_type
    @og_url = @landing_page.full_url
    
    # Twitter Cards
    @twitter_card = @landing_page.twitter_card
    @twitter_title = @landing_page.twitter_title_or_default
    @twitter_description = @landing_page.twitter_description_or_default
    @twitter_image = @landing_page.twitter_image_url
    
    # Schema.org
    @schema_markup = @landing_page.schema_markup_or_default
    
    # Robots
    @robots_meta = @landing_page.robots_meta
  end
end